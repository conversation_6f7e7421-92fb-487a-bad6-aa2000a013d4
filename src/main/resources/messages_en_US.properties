RetCode.Success=Success
RetCode.ServerError=Server Error
RetCode.AuthFailed=Auth Failed
RetCode.PermissionDenied=Permission Denied
RetCode.ValidationError=Validation Error
RetCode.BusinessError=Business Error
customize.msg={0}
year.month.is.over.syncYear=year month is over syncYear
export.itemlistno.empty=The BOM code cannot be empty when exporting
year.or.month.is.null=year or month is null
the.bit.number.data.is.abnormal=The bit number data is abnormal
time.format.error=time format error
factory.id.is.null=factory id is null
factory.id.must.greater.than.zero=factory id max greater than zero (curr value is {0})
Do.not.perform.deletion.operation.without.incoming.data=Do not perform deletion operation without incoming data
lfid.is.null=LFID is null
export.limit.1w=export max limt 10000
batch.lfid.all.null=batch and LFID is must has one value
user.is.not.exists=user is not exists
password.is.error=password is errorbatch.lfid.all.null=batch and LFID is must has one value
premanu.type.exists={0} premanu type exists
tagNum.cannot.be.null=tag number cannot be null
tag.null.only.one=The same material code, only one tag number can be empty for the same processing type
tagNum.cannot.be.same=tag number{0} already exist 
item.no.is.null=please input item no
excel.resolution.failure=excel resolution failure,{0}
excel.import.failure=excel import failed
excel.import.success=excel import success
bom.code.is.null=bom code is null
send.center.psTask.to.sys.failure=send Center PsTask To Sys Failure, param is {0}
bom.data.of.bom.no.not.exist=bom.data of bom no not exist
item.no.contains.one.position.but.not.all.position=positions of item no {0} contains position {1} but do not contains all position {2}
bom.data.repeat=bom data repeat
bom.code.can.not.be.empty=bom.code.can.not.be.empty
The.page.size.should.not.be.larger.than.1000 = The page size should not be larger than 1000
emp.no.is.null=emp no is null
barcode.param.is.null.or.too.many=barcode param is null or too many
classify.is.too.many=classify is too many
bom_prod_params_errro= The bomProd parameter is abnormal
Hard.Cord.File.Template=File Template Error\uFF01Unincluded Column\uFF1A
approval_log_name=Board scrap approval error message
query.export.error=Resource pool maintenance export exception
label.generate.rule.is.null=label generate rule is not null
match.multiple.label.generate.rule=match multiple label generate rule
generate.barcode.failure=generate barcode failure
generate.max.reel.id.count=generate reelId max count is 2000
day.over.one.year=days over one year
duplicate_material_storage_attributes=duplicate material storage attributes
sn.is.null=sn is null
query.flag.error=query flag error(1-CA info,2-check info)
sn.num.is.more.than.100=sn num is more than 100
resource.use.info.export.max=A maximum of 100 resource IDs can be selected at a time
ca.api.call.error=ca api call error
item.no.need.bind.ca=item no need bind ca
task.data.is.error={0}task data error
no.task.need.to.gene=no task need to generate sn
socket.io.exception=io exception(loc:{0})
prodplan.id.is.null=prodplan id is null
xml.to.obj.error=xml to obj error: {0}

barcode.not.exist=barcode not exist
init.barcode.not.exist=init barcode not exist
new.barcode.existed=new barcode existed
incomplete.conditions=incomplete conditions

param.is.null=param is null
params.error=params error:{0}

max.version.is.z=max version is Z
position.do.not.match.the.uploaded.record=position {0} do not match position {1} of the uploaded record
factory.id.or.emp.no.is.null=factory id or emp no is null
data.not.exist.or.has.deleted=data not.exist or has deleted
soft.ver.of.boms.info.not.same=soft ver of boms {0} compare to the last not same
soft.version.not.match=soft version not match
is.uploading.please.wait=is uploading please wait
download.is.failed=download is failed
upload.artifactory.failed=upload artifactory failed! status code {0}
delete.artifactory.failed=delete artifactory failed! status code {0}
download.artifactory.failed=download artifactory failed! status code {0}
soft.version.of.delete.data.is.not.the.latest=soft version {0} of delete data is not the latest
data.or.params.error=data or params error, {0} is blank 
split.task.no.data=Split Task No Data, taskNo is {0} , sourceSys is {1}
board.instruction.cycle.info.is.creating.please.wait=board instruction cycle info is creating please wait
network.license.sign.lock=Network access resources are being imported. Please try again later
network.license.resource.not.fount=Resource No. does not exist
network.license.print.not.fount=The resourceNumber or SN print record does not exist
network.license.resource.print.not.fount={0}Resource No. Print record not exist\uFF01
reel.id.not.register=reel id not register
work.time.error=work time of process{0} should be positive integer range 1 to 2000
remain.time.error=remain time of process{0} should be positive integer range 1 to 2000
last.process.should.be.stock=the last process should be stock
match.task.no.data=match task no data , taskNo is {0} , prodPlanId is {1}
rfid.sign.info.data.is.null=Signature data not transmitted
rfid.sign.info.not.setting=No electronic signature information set
rfid.sign.info.not.salt=Electronic signature encryption salt value is not set
rfid.sign.info.not.cert.id=Electronic signature certificate ID is not set
rfid.sign.info.not.cert.id.get.addr=E-signature certificate ID acquisition address is not set
rfid.sign.info.cert.id.get.fail=Failed to get certificate ID
rfid.sign.info.cert.id.get.error=Exception in obtaining certificate ID
rfid.sign.info.cert.id.oper.success=Certificate ID obtained and updated successfully
board.first.house.service.error= Board first house service error
prodplan.service.error=prodplan service error 
re.work.error=rework scan to check the product category, the entry is empty
task.not.exist.or.not.allow.to.modify=task not exist or not allow to modify,taskNo is {0}
date.error=date error 
at.least.choose.one=DELIVER SETS NUM\u3001CONTRACT NUMBER\u3001TIME AT LEAST CHOOSE ONE
datatype.error=datatype error
datetype.error=datetype error
cycle.type.error=cycle type error
result.type.error=result type error
deliver.sets.service.error=point to point microservice error
cycle.productclass.and.plangroup.error=plan group and product class cannot be selected at the same time
str.format.time.not.null=Time string cannot be empty, string format must be(yyyy-MM-dd HH:mm:ss), demo: 1999-01-01 00:00:00
no.data.to.export=not data export
deliver.entity.service.error=deliver entity to export
bom.server.error=bom server error
afferent.data.exceeds.maximum=afferent data exceeds maximum
no.maximum.value.found=No maximum value found
data.is.empty=data is empty
received.data.not.meet.requirements=The received data does not meet the requirements, please reconfirm and submit
check.the.file.and.the.selected.customer.part.type=Check the file and the selected customer part type
received.data.not.meet.requirements.new=The received data does not meet the requirements, please reconfirm and submit {0}
file_format_error=File format error, please use excel import!
workbook_init_fail=workbook init fail
import.failed.please.try.again=Import failed, please try again!
import.successful.rows=Import successful! Rows:
import.successful=Import successful! 
max.import.is.once=You can only import up to 5000 at once!
subcontractor.id.can.not.empty=Subcontractor ID is not allowed to be empty, row: {0} row.
access.party.code.is.empty=The access party code is empty!
outsourcing.factory.name.is.empty=Outsourcing factory name is empty!
zte.po.is.empty=ZTE po is empty!
outsourcing.work.order.number.is.empty=Outsourcing work order number is empty!
device.sn.en.product.empty=The whole machine sn / en / product / sn is empty!
sn.is.empty=The board sn is empty!
mac.is.empty=mac is empty
product.code.is.empty=Product code is empty!
product.code.not.exist=Product code is not exist!
primary.key.is.empty=Primary key is empty
system.not.support.the.type=The system does not support this type of upload data for the time being
tk.registration.info.not.found=No access party registration information (tk) was found.
pk.registration.info.not.found=No access party registration information (pk) was found.
submited.data.is.invalid=The submitted data is invalid and the verification fails.
submited.data.format.error=The format of the submitted data is incorrect, and no value is passed:
import.failed.please.check.data=Import failed, please check the data in the table and try again!
product.library.cannot.be.empty=Product library package balance is not allowed to be empty, the first:
plan.quantity.cannot.be.empty=Plan quantity cannot be empty
product.categories.cannot.be.empty=Product categories are not allowed to be empty
product.model.cannot.be.empty=Product model can not be empty
product.name.cannot.be.empty=Product name can not be empty
shipped.number.cannot.be.empty=Shipped number cannot be empty
deadline.cannot.be.empty=Deadline cannot be empty
board.balance.cannot.be.empty=Board balance cannot be empty
test.package.balance.cannot.be.empty=test package balance cannot be empty
vendor.cannot.be.empty=Vendor cannot be empty
item.name.cannot.be.empty=Item name cannot be empty
item.model.cannot.be.empty=Item model cannot be empty
material.po.number.cannot.be.empty=Material PO number cannot be empty
this.batch.quantity.cannot.be.empty=This batch quantity cannot be empty
sampling.quantity.cannot.be.empty=Sampling quantity cannot be empty
incoming.date.cannot.be.empty=Incoming date cannot be empty
detection.date.cannot.be.empty=Detection date cannot be empty
detection.no.cannot.be.empty=Detection number cannot be empty
sampling.method.cannot.be.empty=Sampling method cannot be empty
detection.result.is.empty=Detection result is empty
item.code.cannot.be.empty=Item code cannot be empty
item.no.cannot.be.empty=Item no cannot be empty
outsourcing.reelid.is.empty=Outsourcing REEL ID is empty
reelid.quantity.is.empty=REEL ID quantity is empty
correlation.time.is.empty=Correlation time is empty
stock.quantity.is.empty=The stock quantity is empty!
stock.cutoff.time.is.empty=Stock cut-off time is empty!
line_can_not_be_empty=line can not be empty
sn.scan.time.cannot.be.empty=The SN scan time cannot be empty or the format is wrong
feed.quantity.cannot.be.empty=Loading quantity is not allowed to be empty
feed_time_cannot_be_empty=FEED_TIME is not allowed to be empty or malformed
file_name_is_empty=File name is empty
data.table.duplicated=The data in the data sheet is duplicated!
license.plate.number.is.empty=License plate number is empty!
shuttle.error=Shuttle ERROR
phone.number.is.empty=Phone number is empty!
route.is.empty=The route is empty!
tag.sequence.not.exists=No sequence exists for this tag
no.bom.details.data=bom details is not found
material.base.attributes.not.found=No material base attributes were queried
tag.number.analysis.failed=Tag number analysis failed
not.meet.rule=Does not meet the independent number rule,
exist.multiple.connectors=There are multiple connectors,
not.meet.consecutive.number.rule=Does not meet the consecutive number rule,
two.tag.numbers.characters.not.equal=The characters before the two tag numbers are not equal,
start.num.greater.or.equal.end.num=The start bit number is greater than or equal to the end bit number,
no.bom.data.need.split=No BOM data need to split
tag.number.split.failed=Tag number split failed
file.parsing.error=File parsing error
file.parsing.success=File parsing successify
sn.rule.params.error=Barcode rule parameters are abnormal
product.classification.cannot.be.empty=Product classification cannot be empty
exist.in.other.categories=Exists in other broad categories
exist.in.other.categories.new={0}Exists in other broad categories
existed=existed
description=description:
description.new=description:{0}
name=name:
name.new=name:{0}
product.unit.cannot.be.empty=Product unit cannot be empty
incoming.model.is.empty=Incoming model is empty
classification.exist.storage.value=The current classification has a storage target value, which cannot be deleted or modified
model.already.exists=model already exists
click.the.download.address.below=Click the download address below (download the export file)
download.address='> Download address (
address.will.be.at=<p style = 'color: red'> This address will be at
delete.automatically.after=Delete automatically after downloading, please download as soon as possible </ p>
barcode.create.excel.success=Barcode traceability information query, excel success
query.result.is.empty=The query result is empty
create.query.result.excel.failed=Generate query result excel failed
create.query.result.excel.success=Generate query result excel succeed
generate.query.result.excel.failed=Generate query result excel failed: Please regenerate
item.code.prefix.cannot.be.empty=Item code prefix cannot be empty
material.code.prefix.length.be=,The material code prefix length must be
product.type.length.cannot.exceed=Product type length cannot exceed
mac.address.not.maintained=MAC1 address: {0} is not maintained in the MAC address range and cannot be imported
database.table.exist.sn=The table in the database already exists: {0} subpart barcode
date.format.must.be=The production date date cannot be empty and the format must be: yyyy-MM-dd or yyyy / MM / dd! Line:
some.parms.cannot.be.empty=Internal contract number, sub-component code, sub-component bar code, production date, software version, hardware version, MAC1, outer box number, access address, user name, terminal configuration password, and manufacturing process order number cannot be empty! Row:
data.is.error=The data is abnormal, please re-import:
mac.address.invalid=Please enter a valid MAC address
duplicate.bar.code.inside.template=Duplicate bar code inside template
duplicate.gpon.sn.inside.the.template=Duplicate GPON-SN inside the template
duplicate.device.id.inside.template=Duplicate device ID inside template
records.qty.cannot.exceed=The number of records cannot exceed
production.unit.does.not.exist=Production unit does not exist
mac.start.address.less.end=The MAC start address should be less than the end address, and the address range cannot be greater than 2 million.
mac.address.cannot.be.duplicated=MAC address cannot be duplicated
failed.to.get.user.email=Failed to get user email
email.format.incorrect=email format is incorrect, please confirm
email.to.is.empty=email to is empty, please confirm
email.send.err=email send err:{0}
failed.to.get.redis.lock=failed to get redis lock
failed.to.get.registration.info=Failure to obtain registration information
import.data.is.empty=Import data is empty
duplicate.sn=duplicate SN
duplicate.sn.new=duplicate SN {0]
brand.cannot.be.empty=Brand cannot be empty
device.type.cannot.be.empty=device type cannot be empty
model.cannot.be.empty=Model cannot be empty
prod.task.no.cannot.be.empty=Production task number (outsourcing) cannot be empty
manufacturing.process.no.cannot.be.empty=Product BOM / manufacturing process number cannot be empty
mac1.cannot.be.empty=STB MAC1 / MAC1 * required field cannot be empty
sn.cannot.be.empty=PCBA SN1 / Single Board SN1 * Required field cannot be blank
product.sn.cannot.be.empty=STB SN / Product SN * required field cannot be empty
carton.sn.cannot.be.empty=CARTOON SN / Carton SN * Required field cannot be empty
params.cannot.be.empty=Pallet SN / Production date / Power supply SN / Remote control SN / FT test result / FT test time / Burn-In Result aging test result / Burn-In Time aging test duration / MAC configuration result / MAC configuration time * Required fields are not available
part.two.params.cannot.be.empty=Power on / off test result / power on / off test time / whole machine test result / whole machine test time / whole machine verification result / whole machine verification time * Required fields cannot be blank
part.three.params.cannot.be.empty=Factory configuration result / Factory configuration time / Software version number / Logo version number / Factory configuration file name * Required field cannot be blank
product.sn.already.exists=Product SN already exists
transmission.check.failed=Transmission check failed (SHA256)
no.barcode.required.bind.ca.certificate=No barcode required to bind CA certificate
file.upload.failed=file upload failed
generate.key.exception=Generate key exception
encryption.exception=Encryption exception
decryption.exception=Decryption exception
signature.exception=Signature exception
private.key.is.empty=Private key is empty
signing.exception=Signing exception
file.initialization.failed=File initialization failed
file_is_null=file is null
data.error=data error
device.is.bound=Device is bound, please select again
insert.data.operation.failed=Insert data operation failed!
card.machine.info.is.existed=The card machine information already exists, please check
del.data.operation.failed=Delete data operation failed!
update.data.operation.failed=update data operation failed!
departmenet.is.existed=Department name already exists, please check
employee.no.is.existed=Employee number already exists, please check
card.no.is.existed=The card number already exists, please check
scheduled.update.record.execution.failed=Scheduled update record execution failed
interface.call.succeeded=interface call succeeded
interface.call.error=interface call error:{0}
is.creating.please.wait=contract data is creating please wait
file.upload.succeed=File uploaded successfully
duplicate.route.name=Duplicate route name, please rename!
miss.scheduled.date=Item {0} is missing a scheduled date! Please check
duplicate.site.name=Duplicate site name, please re-enter the site name!
stop.already.existed=The stop has already existed, please choose again!
serial.number.is.exists=The serial number already exists, please re-enter it!
sn.generation.failed=Barcode generation failed
sn.generation.success=Barcode generated successfully
sn.bind.ca.failed=Bar code binding CA failed
access.no.is.existed=The access party number already exists, please re-enter
access.name.is.existed=The access party name already exists, please re-enter
bind_success=bind successfuify
operation.success=Successful operation
preProcess.destination.code.existed=Pre-processing destination code already exists, please confirm
generating.excel=Generating excel, please pay attention to the mail
generating.offline.export.excel=Generating excel, Please check the Download Center
processing.failed=Processing failed
operator.cannot.be.empty=Operator cannot be empty
no.data.to.del=There is no data to delete, you can only delete the data you imported!
mac.format.incorrect=The MAC format is incorrect!
failed.export.emp.no.is.empty=Failed to export data, user empNo is empty
reel.id.verify.passed=Reel ID verify passed
reel.id.is.wrong=Reel ID is wrong
abnormal.operation=Abnormal operation
reelid.is.registered=ReelId is registered
number.cannot.exceed=The number of records cannot exceed 5000 lines
catalog.code.cannot.be.empty=Please enter a catalog code
sub.item.code.can.not.be.empty=Please enter a sub-item code
authentication.failed={&quot;message&quot;:&quot;\u7EDF\u4E00\u6743\u9650\u9274\u6743\u4E0D\u901A\u8FC7&quot;}
userType.not.corrent=userType not corrent
max.print.count.is.2000=max print count is 2000
barcode.generate.error=barcode generate error! {0}
resource.application.is.applying=resource application is applying, resourceNo is {0}
resource.application.not.exist=resource application not exist, resource applyId\uFF1A{0}
resource.application.not.newest=The resource application is not the latest resource and cannot be voided
barcode.call.system=call system can not be null
barcode.is.null=barcode can not be null
resource.info.not.exist=resource info not exist, resourceNo is {0}
choreographer.url.type.error=choreographer url type error
choreographer.call.error=choreographer call error
get.itemlistno.error=get itemlistno error
not.lfid.meter=not meter of {0} 
reel.id.registered={0} Reel Id registered
printType.or.printScene.is.null=PrintType or PrintScene is null
taskNo.is.null=taskNo is null
query.style.and.brand.error=query style and brand error
avl.query.error=avl query error
reelid.null=reelid is null
item.no.null=item is null
product.task.is.null=product task is null
reelid.sn.is.null=reelid sn is null
pkcode.info.is.empty=pkcode info is empty
input.more.than.regiter=input more than regiter
blank.generate.error=blank generate error {0}
please.update.printcs=please update printcs
taskStatus.not.allow.to.modify=taskStatus not allow to modify,taskNo is {0}
not.find=file not find
material.file.upload.error=Error in parsing the uploaded material maintenance file.
material.file.upload.empty=To parse the excel table data is empty, please check the format.
material.file.upload.max.beyond= import maximum quantity {0} please re-upload the file.
material.file.upload.message= Number of successful excle imports: {0}, number of failure imports: {1}.
tagNum.null.is.create= The pre-processing with empty workstation number has been maintained for the material and cannot be maintained for the pre-processing with workstation number.
tagNum.null.can.not.create= This material code has been maintained. If the processing position number is not empty, the processing whose processing position number is empty cannot be maintained
reelid.not.exist.in.mes=The original REELID does not exist in the IMES system
tagNum.exist.disabe.create=Pre-processed data that tagNum has been maintained as empty.
tagnum.is.exit=The pre-processing of {1} with the position number of {0} has been maintained, and cannot be maintained any longer.
tagnum.some.is.exit=The position number {0} contains the information of many maintained position numbers {1}, which must be the same.
material.code.has.maintained.the.pre.processing.data=The material code has maintained the pre processing data 
material.code.has.maintained.only.distribution.operation=This material code has maintained the pre processing data {0} only for distribution operation
tagnum.distribution.operation.inconsistent=The distribution operation selected by the current item number is inconsistent with the maintained distribution operation:{0}. Please confirm\uFF01
distribution.operation.is.empty=Distribution operation is empty
tagNum.exist.pre.processing.type.and.destination.is.null=When the item number is not empty, the type and destination of the previous processing cannot be empty
add.pre.manu.info.batch.failed=Failed to add pre processing information, error message:{0}
apply.qty.exceed.can.use.qty=apply qty exceed can use qty, canUseQty:{0}
generate.apply.no.failed=generate apply no failed \uFF01
get.program.bill.info.failed=get program bill info failed!
get.program.sample.info.failed=get program sample info failed!
get.program.small.info.failed=get program small info failed!
currenthandler.is.null=currenthandler is null
sn.lost.board.center=The {0} barcode does not exist in the barcode center.
exist.only.one.bracket=There is only one bracket
update.barcode.fail=Update barcode center barcode fail, {0}
error_msg_set=Line{0}data error message:{1}
confirm_msg_set=Line{0}data confirm message:The currently selected distribution operation is not consistent with the one maintained (or to be maintained)
no.location.data=No location data, please analyzing BOM location first.
supplier.coding.error=supplier coding error,please enter the correct code
item.inventory.is.null=item receiving stock is empty
item.not.check={0} Material inventory is not carried out, and it is not allowed to finish the inventory
task.is.not.this.factory=The unit of production of this task number is not this factory
available.quantity.is.zero=available quantity is zero,resourceNo is {0}
prod.bind.setting.is.saving=productCode {0} is saving\u3002please wait and retry
item.code.has.bind.on.other.process=item code {0} has binded on other process
get.last.package.process.fail=get last package process fail

sys.look.not.config = sys look {0} param not config
faied.check.outsourcing.production.unit = faied to check the outsourcing production unit
you.have.bill.to.approve=you have bill to approve. bill no : {0}
prodPlanId.is.production=prodPlanId is production , prodPlanId is {0}
you.have.timer.synchronize.fail=You have informatica timer data synchronization failure, please deal with it timely!
timer.synchronize.fail=timer {0} synchronization failed
unbinding.setting.exist=Abnormal binding relationship already exists (including daughter card), no need to repeat settings
productcode.is.dealing=productcode {0} is dealing\uFF0Cplease wait
chinese.comma.existed=chinese comma existed\uFF0Cplease confirm!

cad.point.lost.error=The {0} bit number is not in the CAD file. </br> and the {1} material will be uniformly generated on the A side. </br>
cad.point.lost.last.infor=Last update time :{0}, finally new :{1}. </br>
cad.point.lost.error.sure=Please confirm whether to continue the CAD analysis!
cad.point.empty=CAD the file, the available bit number information is empty, please confirm that the file is correct\uFF01\uFF01\uFF01
type_code_name_trace_code_name_can_not_be_empty_alone=Pre processing type, pre processing destination data problem,code and name cannot be empty alone, please confirm!
resource.match.failed=resource number segment format does not match the resource type!
resource.type.should.not.be.null=when enter resource no for query,resource type should not be null
resource.no.str.time.should.not.be.null= when enter resource no for query,resourceNo and resourceStr and time should not be null
search.time.limit= Only the creation time is used as the query condition, and the time range cannot exceed two years
resource.type.can.not.be.null=resource type can not be null
export.time.interval.more.than.180.days=export time interval more than 180 days
export.time.interval.more.than.90.days=export time interval more than 90 days
export.resourceNo.can.not.empty=export resourceno can not empty
export.total.more.than.100000=export total more than 100000
export.time.can.not.null=export time can not null
resource.type.not.exists=resource type not exists
orderOrTask.has.already.applied.for.resources=The task number has already applied for resources
resource.no.does.not.match=the following resource no does not match:{0}
start.segment.must.less.end.segment=The start segment must be less than the end segment
duplicate.resource.number.segment=Duplicate resource number segment, please confirm!
export.timeout=Export timeout
resource.data.error=Inconsistent resource numbers
import.data.error=Error importing resource data
import.timeout=import timeout
import.success=import success
file.format.error=file format error
resource.file.format.error=The {0} tag contains {1} parameters. The imported file contains {2} columns. The quantity does not match. The system cannot process it. Please confirm
resource.file.header.tagparam.error=The imported file header {0} and tagparam {1} does not match\uFF0CPlease confirm
itemCode.selected.cannot.more.than.100=The material list code selected at one time should not exceed 100
unknown.error.handler=There is an unknown error in the system, the error log number is: {0}, please contact the operation and maintenance personnel to deal with it, thank you!
log.id=log id:{0}
task_source_cannot_be_empty=Task source cannot be empty
task_has_been_issued_to_the_local_factory={0} task has been issued to the local factory {1}, please confirm
factory_not_configured_in_1245_data_dictionary=The selected factory has not configured relevant data in the 1245 data dictionary, please confirm!
the_batch_has_generated_a_nesting_list={0} The batch has generated a nesting list, and it is not allowed to cancel the issuance!
get.task.info.error=failed_to_obtain_task_information:{0}
orgid_is_null_or_factoryid_not_find=The organization id or factory id is empty
the_current_batch_is_issued=The current batch not issued, please confirm!
get.spm.tld.error=Failed to obtain nesting list information from SPM 
sequence.beyond.maxvalue=The current serial number exceeds the set maximum value, please check: {0}
sequence.no.value=The initial identification value of the serial number is not set. Please add it manually!
operater_time_can_not_be_empty=Operation time range cannot be empty
operater_time_can_not_greater_one_year=The operation time range cannot be greater than two year
failed.to.get.assembly.relation.push=Failed to get {0} assembly relation push
no.push.record.for.the.material.code=There is no push record for {0} material code version {1}. Please confirm
blank.msg={0}
failed.to.update.result={0}Failed to update the result table and write history according to the material list and version
item.version.null=item version is null
update.successed=update successed
item.code.length.not.twelve=The material code is not 12 digits
taskQty.is.error=taskQty is error
selected.factory.not.the.factory.corresponding.of.task=grant factory is not the factory corresponding to its task organization ID, please confirm
barcode.get.template.error=Failed to call the barcode center to get the template list
barcode.get.template.error.msg=Failed to call the barcode center to get the template list: {0}
get.lookup.value.error=get lookup value error! {0}
failed_to_get_barcode_center_url=Failed to get the URL of the barcode center interface for data dictionary configuration
failed.to.get.barcode.center.download.url=Failed to obtain the download address of the barcode center adapter
failed_to_adjust_barcode_center_print_interface=failed to adjust barcode center print interface
call.barCode.center.to.print.falied=Failed to print barcode in barcode center:{0}
dozen.sn.not.exist.print.data=There is no historical printing data for reprinting barcode {0}. For the records that have not been printed on IMES, please select the printing template
bar.code.center.is.null=To find the record in the bar code center, please check whether theip.is.null=ip is null
ip.is.null=ip is null
item.no.not.exist=This material code information is not found. Please enter the correct material code
print.count.is.null=print count is null
print.count.can.not.more.ten=print count can not more ten
print.num.is.null=print count is null
print.num.can.not.more.one.thousand=print num can not more one thousand
lead.flag.is.null=lead flag is null
print.template.is.null=print template is null
get.printer.failed=get printer name error
failed.to.obtain.hr.info=Failed to obtain information from personnel interface:{0}
taskNo.prodplan.is.null=taskNo prodplan is null
no_box_code_data_obtained=NO BOX CODE DATA OBTAINED
item.type.diff=Item type and interface selected item type are inconsistent, please confirm!
item.not.exists.bom=Item code / BomCode does not exist in the BOM!
item.influence.bom.msg1=This operation will affect the binding relationship of {0} material orders. The system does not process the historical binding relationship, please confirm whether you need to submit!
item.influence.bom.msg2=This operation will affect the binding relationship of {0} material orders, and subsequent\
   new material orders will automatically add binding relationships! Specific material list information: {1}
item.influence.bom.msg3={1} material orders without {0} sub processes (can only be maintained manually at the\
   material order level or by adding a new process path), specific material order information: {2}
item.has.exists=itemNo already has a valid maintenance record, if it cannot be maintained again, please confirm!
route.detail.empty=route detail empty{0}
uac_token_cannot_be_empty=UAC TOKEN cannot be empty, please confirm!
failed_to_tune_icenter_interface=Failed to adjust iCenter interface,URL\uFF1A{0}
failed_to_call_icenter_interface=Failed to tune iCenter interface, MSG: {0}
failed_to_call_icenter_to_withdraw_documents=Failed to call iCenter to withdraw the document, MSG: {0}
the_barcode_length_is_less_than_7_digits=The barcode {0} is less than 7 digits long
failed_to_get_barcode_factory_id=Failed to get the factory id corresponding to barcode {0}
failed_to_write_test_record=The dispatch factory failed to write the test record: {0}
speciality.resource.not.exist={0}resource not exist
speciality.param.template.exist=template name existed
speciality.param.template.not.exist={0}template not exist
speciality.param.template.item.null=parameter list data cannot be empty
speciality.param.template.item.name.repeat=duplicate template parameter name
speciality.param.template.item.brackets=template brackets are not paired\uFF1A{0}
speciality.param.template.item.two.operator=template rule operator error\uFF1A{0}
speciality.param.template.item.function=template rule function error\uFF1A{0}
speciality.param.template.item.rule.err={0}template rule operator error, please confirm!
speciality.param.template.item.error=parameters in template {0} rule do not exist or are sorted incorrectly
speciality.param.applyTask.generate={0}Order number has generated parameters, please confirm
speciality.param.applyTask.not.resource=The {0} order has no {1} resource requisition, and cannot generate parameters. Please confirm
speciality.param.applyTask.resource.not.match=The requested quantity of {0} resource requisition {1} is inconsistent with that of {2} resource requisition {3}. Parameters cannot be generated. Please confirm
excel.read.failed=the file resolution fails
table.column.error=table column error
status.of.taskNo.is.released=status of taskNo:{0} is released
length.of.data.exceeds.500=length of data can not exceeds 500
taskNo.or.prodplanId.fields.cannot.be.empty=taskNo or prodplanId fields cannot be empty
fields.such.as.assembly.remarks.must.not.be.empty=fields after taskNo{0} and prodplanId{1} must not be empty at least one
duplicate.data.in.the.table=duplicate data of taskNo:{0} in.the.table
taskNo.and.prodplanId.do.not.match=taskNo:{0} and prodplanId:{1} do not match
failed_to_process_the_document_in_the_factory=Dispatching factory failed to process document: {0}
failed_to_process_approval_center_kafka_message=Failed to process approval center kafka message
task.no.not.exists=taskNo:{0} not exists
type.name.should.not.be.null=type name should not be null
process.code.id.exists=process code id exists
process.name.exists=process name id exists
station.is.existed=station is existed
insert.workstaion.is.success=add workstation successfully
single.page.query.cannot.exceed.500.entries=a single page query cannot exceed 500 entries
params.is.null=parameter is empty
query.noCtBasic.by.craftId=The current process information has been upgraded, please refresh
existing.uncommit.craft={0} already exists a fictional process
save.sucess=Successfully saved
not.find.craft.info=The corresponding process basic information is not found, please check
must.be.board.assembly=The first process of veneer assembly must be veneer assembly and casting
must.be.board.test=The first process of the single board test must be the single board test and the board
this.version.craft.exists={0}-{1} crafting path already exists in this version
the_current_version_is_malformed=The format of the current version is incorrect, please confirm
the_version_number_is_temporarily_only_supported=The version number currently only supports V.+ three digits, please confirm!
not.find.route.head.info=not find route head info
not.find.route.detail.info=not find route detail info
craft_version_exist_submited=The material list already has submited process data of version {0}
craft_version_exist=The material list already has process data of version {0}
tags.param.is.null=tags param is null, please check
task.no.pulling=The taskNo {0} is pulling data from aps. Please try again later.
task.no.not.exist.aps=The taskNo {0} has no valid data in IAPS. Please confirm.
task.no.exist.imes=The taskNo {0} exists in the IMES and cannot be pulled.
prod.address.empty=The {0} configuration item of the prodAddress is missing. Please check it.
bom.id.miss=BOM data missing bomId:{0}\uFF0CtaskNo: {1}
sn_carton_relation_err=inFor qty {0},billNo:{1} cartonNo:{2}, SN:{3}, not match the actual quantity sent out. Please check!
board.assembly.query.params.not.null=Primary barcode, sub barcode, start/end time must be entered
board.assembly.query.time.not.paired=Start time and end time must be entered at the same time
board.assembly.query.params.not.one.condition=Primary barcode, sub barcode, start/end time cannot be combined
board.assembly.query.page.and.row.not.null=Paging parameters need to be entered when using time interval query
board.assembly.query.time.interval.exceeds.one.month=The time interval cannot exceed 31 days when using time interval query
board.assembly.query.row.too.large=When using time interval query, the number of rows per page cannot exceed 100
recovery.finished=resource recovery,please check
prod.plan.sequence.over = The batch sequence of the central factory reaches the early warning value. Please process \
  it in a timely manner. Current sequence: {0}, maximum sequence: {1},threshold {2}
kafka.msg.save.database= kafka message consumer failed,save to dataBase, Please analyze and handle in a timely \
  manner,messageId {0}
prod.plan.exist.center.factory= {0} prodPlanId exist centerFactory Unavailable
no.resource.recovery=no resource recovery,please check
excel.content.format.not.match=Excel Content and format do not match
the_current_status_of_the_approver_is_finished=The current status of the approver is finished, please confirm
current_document_information_not_found=The current document information is not found,please confirm!
params.err=params err
mainCraftSection.is.null=please choose mainCraftSection
a.maximum.of.ten.data.can.be.queried=A maximum of ten data can be queried
check_that_the_product_code_is_empty=Please check that the product code is empty
aps.proplanId.msg.error=APS prodplanId synchronization message data exception, please check!
factory.id.of.center.pstask.is.null=The factory ID of the task number in the IMES central factory is empty, please confirm!
factory.id.of.center.pstask.is.illegality=The factory ID of the task number in the IMES central factory is illegal, please confirm!
center.factory.not.exist.the.task.no=The task number does not exist in the IME central factory, please confirm!
prodplanno.not.null=The task number cannot be empty
prodplanmodifyno.not.null=The change order number cannot be empty
failed_to_deal_technical_bill=Failed to process the current technical transformation sheet {0}, please confirm!
more_than_50_batches=There are more than 50 online batches of technical transformation sheet {0}, please re bill of lading!
reelid_already_exists=reelid:{0} already exists
kafka.msg.save.database.task.update=The planned task failed to modify kafka message consumption. It has been stored in the local message table. Please analyze and process it in time. Message id: {0}
get.boardonline.info.failed = get boardonline info failed
get.barsubmit.info.failed = get barsubmit info failed
failed.to.update.sys.look.up.meaning=failed to update sys look up meaning
query.params.lost=taskNo,prodplanIdList,itemNoList,sn Must be set one

technical_info_lost=Failed to synchronize technical information, please confirm!
technical_info_outnumber=The number of bar codes exceeds 5 million,and there is a certain delay in transferring to MES. Please pay attention!
aps.task.status.error={0} in APS task is not in released status, and cannot be manually pulled. Please confirm!
the_process_path_of_the_material_list_is_not_found=The process path data of the material list is not found, and the binding relationship is not automatically resolved by the system. Please synchronize the process path first
product.code.include.pcb=For PCBs included in the {0} BOM, click the button for manual calculation.
the.material.list.code.should.contain.15.characters=the material list code should contain 15 characters
no.bom.information.is.found=no bom information is found
synchronize.spm.data.warning=The synchronization of SPM technical transformation information was partially successful. The following data needs to be analyzed and processed: {0}
synchronize.spm.data.error=Failed to synchronize SPM technical transformation information. The synchronization time interval is {0} to {1}. Reason for failure:
get.last.sync.time.error=Failed to get the last synchronization time of SPM. The specific error information is as follows:
spm.data.not.have.chg.reg.no=SPM data in this line has no technical modification order number
spm.data.not.have.prod.id=SPM This line data has no batch number
the_itemnos_is_more_than_three_hundred=The itemnos is more than three hundred
the_itemnos_is_more_than_one_thousand=The itemnos is more than one thousand
params.can.not.be.null=itemNo and itemName should not be null at the same time
the_itemnos_is_more_than_ten=The itemnos is more than one ten\uFF01
query.bom.no.null=not find bom no
param.size.between.1.and.1000=param size between 1 and 1000
task.no.cannot.be.null=The task no cannot be null
send.semi.wip.ext.by.spm.lock=updating semi assembly relationships
send.material.wip.ext.by.spm.lock=updating raw material assembly relationships
no.find.wip.ext.prod.plan=not find prodPlan {0}, this prodplan barcode push wip ext to factory failed\uFF0Cplease confirm\uFF01
push.wip.ext.to.factory.failed=wip ext push to factory failed {0}
failed_to_obtain_prod_plan_imes_batch_information=Failed to obtain prod plan imes batch information
param.missing=Required parameter is missing
query.params.empty.except.create.by=query params empty except create by
is.updating.please.wait=The current bill of materials is being operated, please wait
bom.temperature.is.exited={0}craftSection,{1}itemNo,{2}side,{3},{4},Furnace temperature name already exists, please confirm
failed_to_get_spm_lock_information=Failed to get SPM lock information
failed_to_write_local_factory_lock_information=Failed to write local factory lock information\uFF1A{0}
production_type_already_exists=A process path of production type {0} already exists. You cannot add another process path of production type {1}
the_process_path_of_the_maintained_process_type=The process path of process type {0} has been maintained
the_current_process_path_is_being_processed=The current process path is being processed, please try again later
currently_exporting=Currently exporting
data_volume_exceeds_5_w=The data volume exceeds 5 W and has been exported by mail. Please pay attention to the mail information
lookup.6001.empty=The data field configuration (6001) of board statistics daily report is empty
no.bom.information.or.no.route=no bom information or no craft route detail,please confirm
center.task.query.param.is.null=The task information query parameter is empty, please confirm!
center.task.query.page.or.row.illegal=The page number and number of rows for task information query are illegal. Please confirm!
center.task.query.five.params.not.all.null=The task information query task number, batch, release date, expected release date, and release date conditions cannot all be empty. Please confirm!
center.task.query.time.larger.2.years.when.not.prod.and.task=When the task number and batch are not entered, if the release date, expected release date, or release date are query criteria, the time cannot exceed 2 years (365 days * 2)
customer.items.params.null = parameter is empty
customer.type.null= The type is null
customer.params.null.four = type {0}, customer name, specifications, ZTE code, suppliers are cannot be empty
customer.params.null.other = type {0}, customer name, project name, ZTE code cannot be empty
customer.params.null.one.two.zero=The type is {0}, and the project type cannot be empty
customer.items.add.or.update = current data is submitted, please try again later
check.error.please.reselect.file.parsing = Check error, please re-select file parsing
customer.items.exist= The current material information already exists. Please check {1}
customer.qry.params.null= The project name and time cannot be empty
qry.time.can.not.greater.one.year = range query time can't more than two years
no.id.cannot.delete= Please pass an id
sub.customer.null= Please pass in the subsidiary name
item.no.list.null= Please pass in material code
max.item.no.is.once= A maximum of 500 material codes can be queried at a time
sub.customer.not.exist= Data dictionary 7300 No {0} subsidiary information is configured
size.of.data.too.large=The pushed data cannot exceed 500 pieces at a time, please confirm!
push.time.within.180.days=push time cannot exceed 180 days
log.data.query.param.null=customer data log query param at least input one
log.data.query.param.not,with.time= customerName,messageType,status should query together with pushTime
log.data.lookup.type.error=fail to get log data lookup type,please confirm {0} setting
log.data.not.match.lookup.type=repush data log not match message type in lookup type
sign.illegal=Signature is illegal, please confirm!
url.empty=url empty
zj.customer.model.null=Customer model cannot be empty when type is complete machine
workshop_capacity_already_exists={0}, workshop capacity already exists
spare.part.detail.error=The spare part allocation details contain the data with the name/quantity of the spare part empty.
spare.part.approve.error=There is data with the approver/position empty in the spare part allocation approval details
spare.part.bill.lost=The {0} information of the spare part allocation ticket does not exist.
spare.part.status.error=The status of the spare part allocation ticket is not being drafted or failed to be verified and cannot be modified.
spare.part.status.error.msg=The status of the spare part allocation ticket is not To be verified cannot be modified.
spare.part.partName.error=partName is null
spare.part.quantity.error=The quantity can only be a positive integer
spare.part.partName.repetition=partName repetition
spare.part.factory.error=The receiving factory and the transferring factory cannot be the same
spare.part.approve.lost=Missing approval node, please add {0}
sn.list.of.ca.certificate.null=The standard barcode of the certificate to be imported is empty, please confirm!
sn.list.of.ca.certificate.more.200=The number of standard barcodes for the certificate to be imported exceeds 200 and cannot be imported. Please confirm!
sn.ca.bind.have.sn.info=Barcode has been imported, no need to import again
sn.ca.check.error=Reason for barcode {0} failure: {1};
sn.ca.check.error.head=Some barcode verification failed, as follows:
not.in.the.document.details={0}not in the document details of {1}
spare.part.bill.no.not.null=bill no can not be empty
get.fixture.model.head.id.null=get fixture model fail, head id is null
item.detail.can.not.be.empty=item detail can not be empty
close.bill.detail.allocation.status.wrong=The corresponding spare part transfer status under the transfer ticket is not rolled back or transferred, and cannot be closed. Please confirm!
spare.part.allocation.query.param.null=If the query parameters are incorrect, the codes of the allocation order and spare parts can be queried directly. Other conditions shall be met to cooperate with the query of the receiving time/transfer-out time/allocation order
page.params.of.ca.query.null=Query CA certificate, paging parameters cannot be empty
sn.list.of.ca.query.null=Query CA certificate, the machine barcode cannot be empty
barcode.not.registered=Barcodes have unregistered barcode in barcode center {0},please confirm!
date.range.repeat=Holiday time has repeat range with {0} time {1}
holiday.params.error=Holiday query condition cannot be all null
holiday.repeat=holiday repeat {0} ,please edit or delete elder holiday setting
holiday.date.null=add or edit param data range is null ,please confirm!
holiday.year.error=holiday year is different from year in date range, please confirm!
pws.is.wrong=receiver password is wrong
factory.id.is.same=The currently selected factory is consistent with the factory that has been distributed, and distribution cannot be cancelled
batch_has_a_lock_order=Batch has lock order {0}, Please unlock before canceling the distribution
no.product.has.process.code=The unbound BOM process path does not include {0} sub processes. Please confirm
pls.select.process.code=please select process code
process.code.cannot.be.n=Sub process cannot be a receipt

dimension_null=dimension null
production_unit_null=production unit null
work_order_category_null=work order category null
plan_group_null=plan group null
model_name_null=model name null
utilization_rate_null=utilization rate null
capacity_day_null=capacity day null
model_or_plan_group_more_then_ten=model or plan group more then ten

resource.warning.waterLevel.title=Resources in the resource pool are lower than the water level. Handle the problem immediately!
resource.warning.waterLevel.tip=The following resources are below the water level:
resource.warning.expiryDate.title=There are resources to be expired in the resource pool. Please handle them in a timely manner!
resource.warning.expiryDate.tip=The following resources are about to expire:
resource.warning.resourceNo=Resource No
resource.warning.deviceType=Device Type
resource.warning.lowWaterLevel=Low Water Level
resource.warning.availableQuantity=Available Quantity
resource.warning.expiryDate=Expiry Date
resource.warning.expiryDateBefore=Valid early warning Period\uFF08Day\uFF09


resource.warning.import.check.fail=Failed to import and verify resource early warning rule maintenance.
resource.warning.required.one=The water level or early warning days must be set to one value;
resource.warning.resourceNo.notEmpty=The resource number cannot be null.
resource.warning.deviceType.notEmpty=The device model cannot be null.
resource.warning.resource.exist=The resource ID already exists.
resource.warning.device.exist=The device model already exists.
resource.warning.resource.device.noExist=The relationship between the resource type and the device model does not exist.
resource.warning.import.max=import supports a maximum of 1000 records.
resource.warning.type.notEmpty=The warning dimension cannot be empty;
resource.warning.device.isNUll=The device model does not exist;
resource.warning.resource.isNUll=The resource number does not exist;
resource.warning.water.level.isNumeric=The water level must be a positive integer;
resource.warning.expiry.date.isNumeric=Valid early warning Days must be a positive integer;
resource.warning.excel.resource.isSame=The same resource ID exists in the Excel;
resource.warning.excel.device.isSame=The same device type exists in the Excel;
resource.warning.database.resourceType.device.isSame=Equipment models with the same resource number cannot be entered repeatedly;
resource.warning.database.device.isMultiple=The database has multiple devices of the same model;
resource.warning.database.device.type.isSame=The early warning rule of the device type {1} corresponding to the {0} resource number has been maintained, and cannot be entered repeatedly;
resource.warning.database.resource.same.device.different=Different device models with the same resource number are not allowed to be entered repeatedly;
resource.warning.database.resource.isMultiple=There are multiple identical resource IDs in the database;
resource.warning.database.resource.isEmpty=The resource number database does not exist;
resource.warning.database.resource.device.isEmpty=The database corresponding to the resource number does not exist;
resource.warning.database.resource.device.mismatch=The resource number does not match the device model;
resource.warning.type.notSupport=The system does not support this early warning dimension.

network.license.cert.name=The certificate name only supports "trial access" and "network access license";
network.license.resource.type=The authentication type can only be "network access license";
network.license.file.name.error=File name error. It must be a txt file starting with a 14th digit resource number!
network.license.file.max=A maximum of 300000 lines are supported!
network.license.file.import=File import error!
network.license.file.error=Parsing error!
network.license.sign.exits=The resources in the electronic label document already exist in the system. Do not import them repeatedly!
network.license.sign.file.exits=Repeated. Do not import repeatedly.
network.license.print.shortage={0}Insufficient resources!
Hard.Cord.Time.Format=Time Format\uFF0Cexample: 2022-10-01
get.aps.plan.group.and.model.error=Error in obtaining APS plan group and model information
get.aps.plan.group.and.model.errors=Error in obtaining APS plan group and model information error info :{0}
more.than.max.num=more than max deal value {0}

network.license.binding.isNUll={0} Not exist, please scan the resource Num
network.license.binding.allocated={0}The status is not allocated. Please scan the resource Num
network.license.binding.repeated={0} has been bound to the {1} batch, and cannot be bound repeatedly
resource.no.available.quantity.not.enough=The available quantity of {0} resource number is {1}. Please replace the resource number
date.range.more.than.90.days=date range more than 90 days

current_resource_number_is_operation=The current resource number is currently in operation. Please try again later
operation_time_can_not_greater_three_months=operation time can not greater three months
no_current_barcode_binding_record_found=No current barcode binding record found
failed_to_get_preview_link=Failed to get file preview connection {0}
failed_to_generate_preview_header=Failed to generate file preview authentication header information
approval_comments_cannot_be_empty_when_rejecting=approval comments cannot be empty when rejecting
query.time.and.billNo.is.null=Except for the abnormal order number, all other conditions should be matched with the create date
query.last.time.and.billNo.is.null=Except for the abnormal order number, all other conditions should be matched with the last update time query
time_interval_more_than_half_year=time interval can not be more than half year
last_time_interval_more_than_half_year=last update date interval can not more than half year
the_person_to_be_handed_over_to_can_not_be_null=the person to be handed over to can not be null
the_approval_type_is_incorrect=the approval type is incorrect
fail_to_upload_file=fail to upload file
file.type.illegal=file type illegal
bill_info_input_param_empty=billNo, base, department, and handler cannot be empty
please_maintain_at_least_one=Please maintain at least one document detail
input_sn_is_null_or_duplicate=The barcode in the input details cannot be empty or duplicate. Please confirm
type_or_dec_of_sn_is_null=Please complete the types and descriptions of anomalies in the barcode{0}
qty.of.barcode.can.not.be.null=qty of barcode{0] can not be null
no.corresponding.documents.obtained.in.the.approval.center=no corresponding documents were obtained in the approval center
external.type.is.null={0}external type can not be null
style.is.null={0}style can not be null
operation_time_can_not_greater_ont_months=operation time can not greater one month
operation_time_can_not=The binding time range cannot be null

resource_use_no_is_empty_or_status_error=resource use no is empty or status_error
resource_use_barcode_type_not_correct=resource use barcode type not correct
resource_use_num_is_empty=resourceNum is empty
resource_use_no_is_diff=resourceNo is diff
resource_use_status_not_allow_import=resource status:{0} not allow import
resource_use_num_exist_same=resource num exist same
resource_use_barcode_no_same=resource use barcode no same
resource_use_barcode_type_is_diff=resource use barcode type is diff
resource_use_data_option_error=resource data option error
resource_use_import_error=resource use record import error:
max.export.is.once=A maximum of 1000000 records can be exported at a time\uFF01
exceed.max.export.count=exceed max export count: {0}
resource_use_import_file_is_empty=resource use import file is not empty
scrap.num.bigger.than.data.num=scrap number bigger than data number
scrap.num.over.limit=scrap number over limit
query.resource.scrap.params.error=query resource scrap params error
query.resource.scrap.date.range.over.year=query resource scrap date range over year
network.license.print.num.max=The total number of print resource numbers exceeds 1000
network.license.print.sn.max=The number of printed SNs exceeds 1000
network.license.print.same=The resource number and SN cannot be printed at the same time
cf.user.menu.max.count=The maximum number of favorites reaches: {0} under the current factory. Please delete some before adding them to favorites!
operation_in_progress_please_wait=Operation in progress, please wait
failed_to_call_b2b_interface=Failed to call B2B interface, error message {0}
ai_question_not_found=The issue you inquired about was not found. Please contact the operations personnel
product.code.not.same.error=product code not same error
product.code.length.error=product code length error
mds.in.program.error=mds in program error {0}
product.code.num.over.ten=product code num over ten
call.barCode.center.expandQuery.barCode.falied=Failed to get barcode extension information from barcode Transfer Center: {0}
data.delete.refresh.page=The data has been deleted. Please refresh the page!
customer.param.table.error=Customer parameter {0} configuration table {1} is incorrect. Please confirm!
dqas.error=DQAS interface error,interface info:{0}
xp.auto.sync.failed.title.one=mds sync product code {0} XP info failed
xp.auto.sync.failed.title.two=xp suto sync failed:productCode:{0},pcb name:{1},pcb version:{2},please confirm and execution BOM split\u3002
xp.auto.sync.failed.title.three=xp suto sync failed:productCode:{0},pcb name:{1},pcb version:{2},please confirm and execution BOM split\u3002
xp.auto.sync.failed.msg.two= {0} procduct code is exist in UTS but not used
xp.auto.sync.failed.msg.three={0}product code has no XP info
xp.auto.sync.failed.msg.four={0}product code has no location info
xp.auto.sync.failed.msg=auto xp sync failed, error message : {0}
pcbversion.not.exist=pcbversion not exist
resource.num.max.one.hundred=resource num max one hundred
relationship.verify.not.pass=relationship verify not pass, reason is \uFF1A{0}
change.order.no.data.null=change order no data null
params.lacking=params lacking
not.found.item.info=not found item info
resource.no.rule.invalid=resourceNo rule invalid
resource.step.is.different=resource step is different
params.rule.error=paramsRule error
no.mac.available.resource=no mac available resource
no.gpon.sn.available.resource=no gpon sn available resource
no.available.resource=no cmei available resource
random.numbers.must.greater.than.or.equal.3=random numbers must greater than or equal 3
item.no.is.invalid=itemNo is invalid
task.is.generating=task is generating
the_model_code_of_the_resource_number_is_empty=The model code for resource number {0} is empty and cannot be printed
Please_enter_the_question_content=Please enter the question content
bom.item.use.account = Material code {0},Bom phased usage: {1}, Bom usage: {2} 
bom.exception.message=BOM code {0} BOM abnormal grading {1} please check the BOM, BOM-level pre-processing data!
please.input.one.condition=please input one condition.
the_model_code_is_empty= The model code is empty and cannot be allocated
apply.qty.is.exceed.max=applyQty is exceed max {0}
emp.no.has.undone.task= empNo has undone task
aoi.img.not.exist=aoi img not exist
eigen.value.exceed.max=eigenValue exceed max
sub.customer.config.lost=Subsidiary {0} customer name is not configured {1}
ucs.login.error=UCS system login exception, please confirm!
aps.task.no.is.null=task number is null, please confirm!
task.no.info.is.null=No corresponding information found for the plan tracking number in the central factory, please confirm!
aps.task.no.info.factory.id.is.null= the factory id of task is null, please confirm!
kafka.msg.save.database.derivativeCode.update=The consumption of derivative code Kafka message failed and has been stored in the local message table. Please analyze and handle it in a timely manner. Message ID: {0}
task.no.info.not.exist=No corresponding information was found for tracking number {0} in the central factory. Please confirm!
task.no.transferring=The tracking number for the plan has already been generated or is currently generating a transfer or nesting order. Please confirm!
no_derivative_code_change_information_found=no derivative code change information found from APS
original_bom_information_not_queried=No original BOM information was found on the bill of materials
this_task_version_has_already_been_processed=This task+version has already been processed
write.back.erp.error=write back to erp error\uFF01{0}
consent.available.quantity.is.insufficient=consent available quantity is insufficient
net.work.params.must.existing.simultaneously=net work params must existing simultaneously
user.no.permissions = The current user does not have permission to modify the API. Please contact the project team for assistance!
param_zte_code_not_blank=The input parameter ZTE code cannot be empty!
pack_list_already_existed=customerName{0},customerComponentType{1} pack list already exists, please confirm
failed_to_obtain_ucs_public_key=Failed to obtain UCS public key
cpqd.instance.no.lost=CPQD product instance:{0} FIXBOM code is missing!
icc.mbom.lost=ICC customer {0} two/three-segment code {1} Mbom data is missing!
pdm.mbom.lost=PDM BOM {0} BOM missing!
pdm.mbom.item.icc.mbom.lost=PDM BOM {0}, customer item code {1} is missing in ICC Mbom!
task.fix.bom.error=Task {0} fixBom Generation Failure Cause: {1}.
task.fix.bom.subject=Failed to generate task FixBom. Please follow!
task.list.not.blank=The array of task numbers cannot be empty!
fix.bom.detail.lost=Task {0} FixBom data lost
fix.bom.incomplete=Task {0} has not received customer instruction information and is not allowed to be dispatched to the local factory for production.
split.not.allowed=Task {0} is not allowed to be split.
customer.to.lookupType.config.lost=The mapping relationship between customer {0} and the data dictionary number is not configured.

variable.does.not.exist=variable does not exist
get.url.null=Data dictionary not obtained: URL of {0}
erpstock.null=erpstock.null
level.zero.fix.bom.detail.error=Level 0 fixBom data lost
zte.code.fixbomid.is.null=zte.code.fixbomid.is.null
get.url.null=Data dictionary not obtained: URL of {0}
erpstock.null=erpstock.null
level.zero.fix.bom.detail.error=Level 0 fixBom data lost
zte.code.fixbomid.is.null=zte.code.fixbomid.is.null
get.url.null=Data dictionary not obtained: URL of {0}
erpstock.null=erpstock.null
level.zero.fix.bom.detail.error=Level 0 fixBom data lost
zte.code.fixbomid.is.null=zte.code.fixbomid.is.null
delivery.feedback.is.must=Delivery feedback is must
max.search.tasks=Max search tasks:{0}
sn.data.size.over=Data size can not over {0}
task.no.repeat=The task numbers or abnormal numbers in the imported Excel file must be unique! Please check and then upload again!
abnormal.no.repeat=The abnormal numbers in the imported Excel file must be unique! Please check and then upload again!
submit.failed=submit.failed,and the reason is\uFF1A{0}
task.no.not.empty=Task no can not empty!
task.no.status=The task number status must be one of: In Progress, Scheduled for Production, and Issued\uFF01
delivery.feedback.date.not.empty=The following fields cannot be empty: Estimated Completion Date, Vendor Self-supplied Material Expected Matching Date, All Materials Expected Matching Date, Vendor Self-supplied Material Actual Matching Date, All Materials Actual Matching Date, Estimated Production Start Date, and Actual Production Start Date.
task.delayed.reason.not.empty=If the task number is delayed, the reason for the delay and other relevant information must be filled in.
custom.no.not.alibaba=Must alibaba task
delivery.feedback.no.receiver=The production delivery feedback email configuration directory code: {0} has not maintained any recipients.
customer.code.name.not.match=The correspondence between customer code: {0} and name has not been maintained.
task.no.not.match.record=Task Number: {0} does not have a corresponding production delivery feedback record.variable.does.not.exist=variable does not exist
failed_to_call_mds_interface=Failed to adjust MDS interface: {0}