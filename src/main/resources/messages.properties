RetCode.Success=\u64CD\u4F5C\u6210\u529F
RetCode.ServerError=\u670D\u52A1\u5668\u9519\u8BEF
RetCode.AuthFailed=\u8BA4\u8BC1\u5931\u8D25
RetCode.PermissionDenied=\u6CA1\u6709\u6743\u9650
RetCode.ValidationError=\u9A8C\u8BC1\u5931\u8D25
RetCode.BusinessError=\u4E1A\u52A1\u5F02\u5E38

reelid.not.register=reelId\u672A\u6CE8\u518C
prodplanid.not.belongs.to.workorder=\u7269\u6599\u6279\u6B21\u4E0D\u662F\u8BE5\u6307\u4EE4\u7684\uFF0C\u8BF7\u786E\u8BA4
new.pkcode.count.must.greater.than.zero=\u65B0\u6599\u76D8\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0\uFF0C\u8BF7\u786E\u8BA4
item.code.not.match=\u63A5\u6599\u9519\u8BEF\uFF0C\u65B0\u6599\u76D8\u7269\u6599\u4EE3\u7801\u3010{0}\u3011\u4E0E\u65E7\u6599\u76D8\u7269\u6599\u4EE3\u7801\u3010{1}\u3011\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
reel.has.been.used=\u8BE5\u6599\u76D8\u5DF2\u88AB\u5360\u7528\uFF0C\u6307\u4EE4\u53F7\u4E3A\uFF1A\u3010{0}\u3011
reel.is.using.please.confirm=\u8BE5\u6599\u76D8\u5904\u4E8E\u5728\u7528\u4E2D\uFF0C\u4E0D\u80FD\u63A5\u6599\uFF0C\u8BF7\u786E\u8BA4
avl.server.failed=\u8C03\u7528AVL\u670D\u52A1\u5931\u8D25
avl.check.failed=AVL\u6821\u9A8C\u4E0D\u901A\u8FC7\uFF0C\u8BF7\u786E\u8BA4

scrap.updateTaskQty.failed = \u6309\u6279\u6B21\u66F4\u65B0pstask\u8868\u5931\u8D25
direction.error.forbidden.receive.items = \u4E0D\u540C\u5382\u5BB6\u7269\u6599\u5728\u7F16\u5E26\u5185\u65B9\u5411\u4E0D\u4E00\u81F4\uFF0C\u7981\u6B62\u63A5\u6599

call.basicsetting.failure=\u8C03\u7528\u57FA\u7840\u670D\u52A1\u5931\u8D25
static.workshop.output.failure=\u7EDF\u8BA1\u8F66\u95F4\u5404\u7EBF\u4F53\u4EA7\u51FA\u65F6\u8BA1\u7B97\u5931\u8D25
not.maintain.manpower=\u4EE5\u4E0B\u7EBF\u4F53\u672A\u7EF4\u62A4\u7EBF\u4F53\u4EBA\u529B\u6570\u636E:{0}
manual.statistic.info=\u624B\u52A8\u7EDF\u8BA1\u63D0\u4EA4\u6210\u529F\uFF0C\u9884\u8BA15\u5206\u949F\u53EF\u7EDF\u8BA1\u7ED3\u675F\uFF0C\u82E5\u7EDF\u8BA1\u5F02\u5E38\u5C06\u90AE\u4EF6\u544A\u77E5

item.not.match_bom.item=\u7269\u6599\u4EE3\u7801\u4E0E\u4E0A\u6599\u8868\u4E0D\u4E00\u81F4\uFF0C\u7AD9\u4F4D\uFF1A{0}\uFF0C\u8BF7\u786E\u8BA4
order.item.empty= \u6307\u4EE4\u6599\u5355\u4EE3\u7801\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
transfer.scan.interactive.error=\u7AD9\u4F4D\uFF1A{0}\uFF0C\u7269\u6599\u4EE3\u7801\uFF1A{1}\uFF0C\u8F6C\u673A\u626B\u63CF\u62A5\u9519
location.not.feeder.bound=\u7AD9\u4F4D\uFF1A{0}\uFF0C\u6CA1\u6709feeder\u7ED1\u5B9A
feeder.not.found.eqp.status=feeder\u7F16\u53F7\uFF1A{0}\uFF0C\u6CA1\u6709\u627E\u5230feeder\u8BBE\u5907\u72B6\u6001\u4FE1\u606F
feeder.full.station.position.empty=feeder\u8BBE\u5907\u7AD9\u4F4D\u4E3A\u7A7A
feeder.location.not.match.bom=feeder\u8BBE\u5907\u7AD9\u4F4D{0}\u4E0E\u4E0A\u6599\u8868\u7AD9\u4F4D{}\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4
workorder.not.find=\u627E\u4E0D\u5230\u6307\u4EE4\u4FE1\u606F

condition.not.find=\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
form.type.not.find=\u7ED1\u5B9A\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
must.has.other.condition=\u9664\u7ED1\u5B9A\u7C7B\u578B\u5916\u5FC5\u987B\u8FD8\u6709\u5176\u4ED6\u6761\u4EF6
export.time.than.two.month=\u5BFC\u51FA\u65F6\u95F4\u4E0D\u80FD\u5927\u4E8E\u4E24\u4E2A\u6708

feeder.bind.valid.failure= \u9A8C\u8BC1\u5931\u8D25 feedar\u7ED1\u5B9A\u5B8C\u6210\u624D\u80FD\u5907\u6599
item.code.empty= \u7269\u6599\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
work.order.empty= \u6307\u4EE4\u4E0D\u80FD\u4E3A\u7A7A
line.code.empty= \u7EBF\u4F53\u4E0D\u80FD\u4E3A\u7A7A
module.no.empty= \u6A21\u7EC4\u4E0D\u80FD\u4E3A\u7A7A
location.no.empty= \u5DE5\u7AD9\u4E0D\u80FD\u4E3A\u7A7A

sn.parent=
qty.error=
source.task.erro=
parent.error=
sn.error=
no.sn.binded=
receive.must.after.qc.transform=QC\u8F6C\u673A\u626B\u63CF\u5B8C\u6BD5\u624D\u80FD\u63A5\u6599
receive.must.after.qc.recheck=QC\u63A5\u6599\u590D\u68C0\u4E4B\u540E\u624D\u80FD\u7EE7\u7EED\u5907\u6599
no.maintain.skip=\u53CC\u8F68\u7EBF\u4E2D\u7684\u53E6\u4E00\u6761\u7EBF\u4F53\u7269\u6599\u672A\u7EF4\u62A4\u7BA1\u63A7\u8DF3\u8FC7

str.valid.error = \u53C2\u6570\u6821\u9A8C\u672A\u901A\u8FC7\uFF0C\u6821\u9A8C\u89C4\u5219\uFF1A\u4EFB\u52A1\u53F7\u4E0E\u5DE5\u5E8F\u6307\u4EE4\u53F7\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
transfer.box.is.null = \u8F6C\u4EA4\u7BB1\u53F7\u4E3A\u7A7A
get.box.info.error = \u83B7\u53D6\u7BB1\u53F7\u5185\u5BB9\u5931\u8D25
transfer.error = \u8F6C\u4EA4\u6570\u636E\u5F02\u5E38
get.box.workOrder.error = \u83B7\u53D6\u7BB1\u53F7\u5185\u5BB9\u6307\u4EE4\u4FE1\u606F\u5931\u8D25

insert.sn.err = \u63D2\u5165\u5355\u677F\u6761\u7801\u5931\u8D25
sn.exis.wipinfo.err = \u7CFB\u7EDF\u4E2D\u5DF2\u7ECF\u5B58\u5728\u8BE5\u6761\u7801 
sn.exis.whitelistinfo.err = \u8BE5\u6761\u7801\u5728\u767D\u540D\u5355\u4E2D\u5DF2\u5B58\u5728
sn.no.exis = \u8BE5\u5355\u677F\u6761\u7801\u6210\u529F\u626B\u63CF

not.get.workshop.code = \u672A\u83B7\u53D6\u5230\u8F66\u95F4\u7F16\u7801
sn.and.apply.time.and.receive.time.is.null = \u6761\u7801\u3001\u7533\u8BF7\u65F6\u95F4\u6216\u63A5\u6536\u65F6\u95F4\u5FC5\u987B\u81F3\u5C11\u8F93\u5165\u4E00\u9879
data.repair.status.is.not.to.be.received = \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u5F85\u63A5\u6536
data.repair.status.is.not.fiction= \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D
data.repair.status.is.not.to.be.received.or.maintenance= \u8BE5\u6570\u636E\u72B6\u6001\u4E0D\u662F\u5F85\u63A5\u6536\u6216\u8005\u5F85\u7EF4\u4FEE
no.permisssion.to.reject.in.received.status = \u5F85\u63A5\u6536\u72B6\u6001\u4E0B\u8BE5\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650
no.permisssion.to.reject.in.maintenance.status= \u5F85\u7EF4\u4FEE\u72B6\u6001\u4E0B\u8BE5\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650

failed.to.get.aps.stock=\u83B7\u53D6APS\u7CFB\u7EDF\u4E2D\u7684\u4EA4\u8D27\u4ED3\u5931\u8D25
sn.work.station=\u6761\u7801 {0} \u5DE5\u7AD9\u4E0D\u662F\u5728\u5E93
sn.can.not.empty=\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
sn.all.not.found=\u6240\u6709\u6761\u90FD\u4E0D\u5728\u5728\u5236\u8868\u4E2D
sn.process.code=\u6761\u7801 {0} \u5B50\u5DE5\u5E8F\u4E0D\u662F\u5165\u5E93
sn.no.wip=\u6761\u7801 {0} \u4E0D\u5728\u5728\u5236\u8868\u4E2D
lookup.6001.empty=\u5355\u677F\u7EDF\u8BA1\u65E5\u62A5\u6570\u636E\u5B57\u6BB5\u914D\u7F6E\uFF086001\uFF09\u4E3A\u7A7A
export.data.more.than=\u5BFC\u51FA\u6570\u636E\u91CF\u5927\u4E8E {0}\uFF0C\u8BF7\u7F29\u5C0F\u67E5\u8BE2\u8303\u56F4
date.range.too.long=\u67E5\u8BE2\u65F6\u95F4\u533A\u95F4\u592A\u957F
date.range.is.empty=\u67E5\u8BE2\u65F6\u95F4\u533A\u95F4\u4E3A\u7A7A

sn.not.in.bill=\u6761\u7801{0}\u4E0D\u5728\u8BE5\u5165\u5E93\u5355\u4E2D\u6216\u6761\u7801\u72B6\u6001\u4E0D\u662F\u5DF2\u63D0\u4EA4
bill.can.not.close=\u5355\u636E\uFF1A{0}\u6CA1\u6709\u626B\u63CF\u63A5\u6536\uFF0C\u4E0D\u80FD\u4F7F\u7528\u5173\u95ED\u529F\u80FD

box.is.null=\u7BB1\u53F7\u4E3A\u7A7A
box.content.is.null=\u5355\u636E\u4E2D\u4E0D\u5B58\u5728\u5DF2\u63D0\u4EA4\u7684\u6761\u7801\u5728\u8BE5\u5BB9\u5668\u4E2D
box.content.not.all.in.bill=\u64CD\u4F5C\u5931\u8D25\uFF0C\u7BB1\u4E2D\u6761\u7801\u5B58\u5728\u4E0D\u5728\u5355\u636E\u53F7\u4E2D\u7684\u6761\u7801
bill.be.scan.receive=\u5355\u636E{0}\u88AB\u626B\u63CF\u63A5\u6536\u4E86\uFF0C\u4E0D\u80FD\u518D\u4F7F\u7528\u63A5\u6536\u529F\u80FD
bill.can.not.reject=\u5355\u636E{0}\u88AB\u626B\u63CF\u63A5\u6536\u4E86\uFF0C\u4E0D\u80FD\u518D\u4F7F\u7528\u62D2\u7EDD\u529F\u80FD
cannot.reason.not.blank=\u4E0D\u80FD\u7ED1\u5B9A\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
production.not.match.plan=\u6599\u5355\u4EE3\u7801\u548C\u6279\u6B21\u4E0D\u5339\u914D

req.bill.no.is.null=\u65E0\u6548\u7684\u9700\u6C42\u5355\u53F7\u6216\u9700\u6C42\u6570\u91CF
commit.qty.morethan.max=\u63D0\u4EA4\u6570\u91CF\u5927\u4E8E\u9700\u6C42\u5355\u6700\u5927\u53EF\u5165\u5E93\u6570\u91CF
req.bill.is.lock=\u8BE5\u9700\u6C42\u5355\u5DF2\u88AB\u9501\u5B9A
warehouse.mode.req.empty=\u6574\u673A\u5165\u5E93\u5355\u7684\u9700\u6C42\u5355\u4E0D\u80FD\u4E3A\u7A7A


no.fallback.privilege=\u7528\u6237\u6CA1\u6709\u56DE\u9000\u6743\u9650
process.fallback.is.not.support=\u8BE5\u5DE5\u5E8F\u4E0D\u652F\u6301\u56DE\u9000
no.reject.permission=\u5F53\u524D\u7528\u6237\u6CA1\u6709\u9A73\u56DE\u6743\u9650
item.civ=CIV\u7BA1\u63A7\u7269\u6599\uFF0C\u4E0D\u5141\u8BB8\u9000\u56DE\u7EBF\u8FB9\u4ED3

item.code.not.in.bind.list=\u6761\u7801{0}\u5BF9\u5E94\u7684\u7269\u6599\u4EE3\u7801\u4E0D\u5728\u5F85\u7ED1\u5B9A\u6E05\u5355\u4E2D
locked.bill.no.str=\u4EE5\u4E0B\u5355\u636E\u53F7\u6B63\u5728\u88AB\u64CD\u4F5C\uFF0C\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4\uFF1A{0}
sn.must.be.greater=\u6761\u7801\u5E8F\u53F7\u987B\u4ECE1\u5F00\u59CB
wip.daily.statistic.job.running=\u5355\u677F\u9AD8\u7EA7\u65E5\u62A5\u7EDF\u8BA1\u4EFB\u52A1\u5DF2\u6267\u884C
erp.warehousing.failed=ERP\u5165\u5E93\u5931\u8D25{0}
repair.status.error=\u975E\u62DF\u5236\u72B6\u6001\u5355\u636E\uFF0C\u4E0D\u80FD\u63D0\u4EA4
center.factory.bom.error=\u8C03\u7528\u4E2D\u5FC3\u5DE5\u5382\u67E5\u8BE2bom\u5206\u9636\u5931\u8D25
b.smt.bom.info.empty={0}\u5DE5\u5E8F\u5BFC\u5165\u7684\u4E0A\u6599\u8868\u4E3A\u7A7A\u6216\u7528\u91CF\u90FD\u4E3A0
workorder.not.found.prodplanid=\u6279\u6B21 {0} \u65E0\u6307\u4EE4
call.service.error=\u8C03\u7528\u670D\u52A1\u5F02\u5E38\uFF1A{0}--{1}
lock_bill_no_is_empty=\u9501\u5B9A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
un_lock_user_no_is_empty=\u89E3\u9501\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
must_be_less_than_1000=
source.task.lock.error.msg=
un_lock_reason_is_empty=\u89E3\u9501\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
lock_bill_no_not_exist=\u5355\u53F7\u4E0D\u5B58\u5728
unlock_sn_is_empty=\u89E3\u9501\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
unlock_plan_is_empty=\u89E3\u9501\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
modifier.is.empty=
unlock_craft_sn_all_empty=\u89E3\u9501\u6761\u7801\u548C\u5DE5\u5E8F\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
sn_more_than_1000=\u6761\u7801\u6570\u4E0D\u80FD\u8D85\u8FC71000
preparation_can_not_unlock=\u62DF\u5236\u4E2D\u5355\u636E\u4E0D\u80FD\u89E3\u9501
tar_work_order_is_null=\u76EE\u6807\u6307\u4EE4\u4E0D\u80FD\u4E3A\u7A7A
work_order_no_smt_detail=\u6307\u4EE4 {0} \u5728 {1} \u6A21\u7EC4\u6CA1\u6709\u4E0A\u6599\u8BE6\u7EC6\u8868\u4FE1\u606F
work_order_module_no_prepare=\u5907\u6599\u672A\u5B8C\u6210\uFF0C\u6307\u4EE4 {0} \u6A21\u7EC4 {1} \u7AD9\u4F4D\uFF1A{2}
line_module_has_mouting=\u7EBF\u4F53 {0} \u6A21\u7EC4 {1} \u5B58\u5728\u6709\u6548\u673A\u53F0\u5728\u7528\uFF0C\u4E0D\u53EF\u5207\u6362
location_not_exist=\u7AD9\u4F4D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
line_no_sup_model_chg=\u7EBF\u4F53 {0} \u4E0D\u652F\u6301\u6309\u6A21\u7EC4\u6362\u7EBF\uFF0C\u8BF7\u786E\u8BA4
line_no_sup_in_feeder=\u7EBF\u4F53 {0} \u4E0D\u652F\u6301\u667A\u80FDFeeder\uFF0C\u8BF7\u786E\u8BA4
feeder_in_use=feeder {0} \u6B63\u5728\u4F7F\u7528\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A
reel_id_in_use=reelId {0} \u6B63\u5728\u4F7F\u7528\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A
reel_feeder_both_bound=reel id \u548C feeder \u90FD\u6709\u7ED1\u5B9A\u8BB0\u5F55
reel_feeder_both_replace=\u540C\u65F6\u66FF\u6362\u7684 reelId \u548C feeder \uFF0C\u90FD\u4E0D\u80FD\u6709\u7ED1\u5B9A\u8BB0\u5F55
failed_to_get_preview_link=\u83B7\u53D6\u6587\u4EF6\u9884\u89C8\u8FDE\u63A5\u5931\u8D25{0}
failed_to_generate_preview_header=\u751F\u6210\u6587\u4EF6\u9884\u89C8\u9274\u6743\u5934\u4FE1\u606F\u5931\u8D25
fail_to_upload_file=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
no_tech_chg_detail=\u6CA1\u6709\u6280\u6539\u8BE6\u7EC6\u4FE1\u606F
no_pdm_tech_chg_info=\u6CA1\u6709PDM\u6280\u6539\u4FE1\u606F
plan_lock_not_exist=\u6279\u6B21\u9501\u5B9A\u5355\u4E0D\u5B58\u5728
tec_chg_not_in_preparation=\u6280\u6539\u5355\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D
task_no_tec_chg_craft=\u6279\u6B21{0}\u672A\u9009\u62E9\u7BA1\u63A7\u5DE5\u5E8F
chg_req_base_is_null=\u6280\u6539\u57FA\u672C\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
pre_chg_file_null=\u8BF7\u4E0A\u4F20\u63D0\u524D\u6280\u6539\u6587\u4EF6
tec_chg_email_send_null=\u6280\u6539\u901A\u77E5\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
tec_chg_can_not_submit=\u5F53\u524D\u72B6\u6001\u4E0D\u80FD\u63D0\u4EA4\u6280\u6539
tec_chg_file_is_null=\u8BF7\u4E0A\u4F20\u6280\u6539\u6587\u4EF6
task_exceed_50=\u6280\u6539\u672A\u5B8C\u5DE5\u6279\u6B21\u4E0D\u80FD\u8D85\u8FC750\u4E2A
work_order_not_finished=\u6307\u4EE4{0} \u672A\u5B8C\u5DE5\uFF0C\u4E0D\u5141\u8BB8\u89E3\u7ED1\uFF0C\u8BF7\u66F4\u6362feeder
pmOrgTransferOrder.transferQuantity.required=\u7269\u6599\u4EE3\u7801\u5B58\u5728\u65F6\uFF0C\u8F6C\u79FB\u6570\u91CF\u5FC5\u586B
transfer.qty.more.warehouse.qty=\u8F6C\u79FB\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7\u4ED3\u5E93\u6570\u91CF
repairApproval.positionNumber.required=\u7EF4\u4FEE\u5BA1\u6279\u4F4D\u53F7\u5FC5\u586B
attach.file.list.not.equal=\u9644\u4EF6\u5217\u8868\u6570\u636E\u5F02\u5E38\uFF0C\u8BF7\u6E05\u7A7A\u9644\u4EF6\u5217\u8868\u91CD\u65B0\u4E0A\u4F20\u540E\u518D\u8BD5
attach.file.list.over.ten=\u9644\u4EF6\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC710\u4E2A
approver_cannot_be_empty_common=
appendix_info_save_failed=
position.code.not.exist=\u4F4D\u53F7\u4E0D\u5B58\u5728
exist.repeat.approver=\u5B58\u5728\u91CD\u590D\u5BA1\u6279\u4EBA\uFF0C\u8BF7\u786E\u8BA4
failed.to.process.approval.center.kafka.message=\u5904\u7406\u5BA1\u6279\u4E2D\u5FC3kafka\u6D88\u606F\u5931\u8D25
only.approving.can.withdraw=\u53EA\u6709\u5BA1\u6279\u4E2D\u53EF\u4EE5\u64A4\u9500
repairApproval.approvalOperateInfo.required=\u5BA1\u6279\u4EBA\u4FE1\u606F\u6709\u7F3A\u5931\uFF0C\u8BF7\u786E\u8BA4
least.create.date=\u8BF7\u81F3\u5C11\u8F93\u5165\u521B\u5EFA\u65F6\u95F4
only.creater.can.withdraw=\u4EC5\u63D0\u4EA4\u4EBA\u53EF\u64A4\u56DE
