RetCode.Success=\u64CD\u4F5C\u6210\u529F
RetCode.ServerError=\u670D\u52A1\u5668\u9519\u8BEF
RetCode.AuthFailed=\u8BA4\u8BC1\u5931\u8D25
RetCode.PermissionDenied=\u6CA1\u6709\u6743\u9650
RetCode.ValidationError=\u9A8C\u8BC1\u5931\u8D25
RetCode.BusinessError=\u4E1A\u52A1\u5F02\u5E38

factory.id.is.null=factory id is null
factory.id.must.greater.than.zero=factory id max greater than zero (curr value is {0})
lfid.is.null=LFID is null
batch.lfid.all.null=batch and LFID is must has one value
user.is.not.exists=user is not exists
password.is.error=password is error

label.generate.rule.is.null=label generate rule is not null
match.multiple.label.generate.rule=match multiple label generate rule
generate.barcode.failure=generate barcode failure
generate.max.reel.id.count=generate reelId max count is 2000

rfid.sign.info.data.is.null=Signature data not transmitted
rfid.sign.info.not.setting=No electronic signature information set
rfid.sign.info.not.salt=Electronic signature encryption salt value is not set
rfid.sign.info.not.cert.id=Electronic signature certificate ID is not set
rfid.sign.info.not.cert.id.get.addr=E-signature certificate ID acquisition address is not set
rfid.sign.info.cert.id.get.fail=Failed to get certificate ID
rfid.sign.info.cert.id.get.error=Exception in obtaining certificate ID
rfid.sign.info.cert.id.oper.success=Certificate ID obtained and updated successfully
board.first.house.service.error= Board first house service error
date.error=
datatype.error=
datetype.error=
cycle.type.error=
result.type.error=
cycle.productclass.and.plangroup.error=
str.format.time.not.null=
no.data.to.export=
deliver.entity.service.error=
bom.server.error=


barcode.call.system=\u8C03\u7528\u7CFB\u7EDF\u4E0D\u80FD\u4E3A\u7A7A
barcode.is.null=\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
choreographer.url.type.error=\u7F16\u6392\u5668\u63A5\u53E3\u5730\u5740\u6570\u636E\u5B57\u5178\u672A\u914D\u7F6E
choreographer.call.error=\u7F16\u6392\u5668\u63A5\u53E3\u8C03\u7528\u51FA\u9519
not.lfid.meter=\u4E0D\u662F {0} \u7684\u7269\u6599
reel.id.registered={0} Reel Id \u5DF2\u6CE8\u518C
update.barcode.fail=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u66F4\u65B0\u6761\u7801\u63A5\u53E3\u5931\u8D25\uFF0C{0}
no.location.data=\u65E0\u4F4D\u53F7\u6570\u636E\uFF0C\u8BF7\u5148\u8FDB\u884CBOM\u4F4D\u53F7\u89E3\u6790
unbinding.setting.exist=\u7ED1\u5B9A\u5F02\u5E38\u5173\u7CFB\u5DF2\u5B58\u5728\uFF08\u5305\u542B\u5B50\u5361\uFF09\uFF0C\u65E0\u9700\u91CD\u590D\u8BBE\u7F6E
timer.synchronize.fail=
you.have.timer.synchronize.fail=
cad.point.lost.error=
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
fix.bom.detail.lost=
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
