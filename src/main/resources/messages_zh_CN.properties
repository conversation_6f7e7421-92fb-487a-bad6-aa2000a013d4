customize.msg={0}
RetCode.Success=\u64CD\u4F5C\u6210\u529F
RetCode.ServerError=\u670D\u52A1\u5668\u9519\u8BEF
RetCode.AuthFailed=\u8BA4\u8BC1\u5931\u8D25
RetCode.PermissionDenied=\u6CA1\u6709\u6743\u9650
RetCode.ValidationError=\u9A8C\u8BC1\u5931\u8D25
RetCode.BusinessError=\u4E1A\u52A1\u5F02\u5E38
year.month.is.over.syncYear=\u5F53\u524D\u6708\u4EFD\u6570\u636E\u672A\u540C\u6B65
year.or.month.is.null=\u5E74\u6708\u4E0D\u80FD\u4E3A\u7A7A
the.bit.number.data.is.abnormal=\u4F4D\u53F7\u6570\u636E\u5F02\u5E38
no.data.query=\u5F53\u524D\u6708\u4EFD\u6CA1\u6709\u53EF\u67E5\u8BE2\u7684\u6570\u636E
time.format.error=\u65F6\u95F4\u683C\u5F0F\u9519\u8BEF
factory.id.is.null=\u5DE5\u5382ID\u4E0D\u80FD\u4E3A\u7A7A
factory.id.must.greater.than.zero=\u5DE5\u5382ID\u5FC5\u987B\u5927\u4E8E\u96F6(\u5F53\u524D\u503C\u662F{0})
lfid.is.null=LFID\u4E0D\u80FD\u4E3A\u7A7A
export.limit.1w=\u5BFC\u51FA\u603B\u6570\u4E0D\u80FD\u8D85\u8FC71w\u6761\uFF0C\u8BF7\u7F29\u5C0F\u8303\u56F4
batch.lfid.all.null=\u6279\u6B21/LFID\u5FC5\u987B\u8F93\u5165\u4E00\u4E2A
premanu.type.exists={0}\u524D\u52A0\u5DE5\u7C7B\u578B\u5B58\u5728
user.is.not.exists=\u7528\u6237\u4E0D\u5B58\u5728
password.is.error=\u5BC6\u7801\u9519\u8BEF
tagNum.cannot.be.null=\u5F53\u524D\u6599\u5355\u4EE3\u7801\u3001\u7269\u6599\u4EE3\u7801\u3001\u524D\u52A0\u5DE5\u7C7B\u578B\u4E0B\u5B58\u5728\u524D\u52A0\u5DE5\u4FE1\u606F\uFF0C\u4F4D\u53F7\u4E0D\u80FD\u4E3A\u7A7A
tag.null.only.one=\u540C\u4E00\u4E2A\u7269\u6599\u4EE3\u7801\uFF0C\u540C\u524D\u52A0\u5DE5\u7C7B\u578B\u53EA\u80FD\u6709\u4E00\u4E2A\u4F4D\u53F7\u4E3A\u7A7A
tagNum.cannot.be.same=\u5F53\u524D\u524D\u52A0\u5DE5\u7C7B\u578B\u4E0B\u5B58\u5728\u4F4D\u53F7{0}\u7684\u8BB0\u5F55
item.no.is.null=\u8BF7\u8F93\u5165\u7269\u6599\u4EE3\u7801
excel.resolution.failure=excel \u89E3\u6790\u5931\u8D25,{0}
excel.import.failure=excel \u5BFC\u5165\u5931\u8D25
excel.import.success=excel \u5BFC\u5165\u6210\u529F
bom.code.is.null=\u6599\u5355\u4EE3\u7801\u4E3A\u7A7A
send.center.psTask.to.sys.failure=\u63A8\u9001\u4E2D\u5FC3\u5DE5\u5382\u4EFB\u52A1\u5230\u672C\u5730\u5DE5\u5382\u5931\u8D25\uFF1A{0}
bom.data.of.bom.no.not.exist=\u6599\u5355\u4EE3\u7801\u5BF9\u5E94bom\u6570\u636E\u4E0D\u5B58\u5728
item.no.contains.one.position.but.not.all.position=\u7AD9\u4F4D\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4! \u7269\u6599\u4EE3\u7801 {0} \u5BF9\u5E94\u8D27\u4F4D\u5305\u542B\u8D27\u4F4D {1} \u4F46\u4E0D\u5305\u542B\u8F93\u5165\u7684\u5168\u90E8\u4F4D\u53F7 {2}
bom.data.repeat=\u6599\u5355\u4FE1\u606F\u6570\u636E\u91CD\u590D\u8BF7\u68C0\u67E5
bom.code.can.not.be.empty=\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
soft.ver.of.boms.info.not.same=\u6599\u5355\u4FE1\u606F\u7B2C {0} \u884C\u548C\u4E0A\u4E00\u884C\u8F6F\u4EF6\u7248\u672C\u4E0D\u4E00\u81F4\u3002\u8BF7\u68C0\u67E5
soft.version.not.match=\u63D0\u4EA4\u7248\u672C\u548C\u5E94\u63D0\u4EA4\u7248\u672C\u4E0D\u4E00\u81F4\u3002\u8BF7\u91CD\u8BD5
is.uploading.please.wait=\u6B63\u5728\u4E0A\u4F20\u3002\u8BF7\u7A0D\u540E
download.is.failed=\u4E0B\u8F7D\u6587\u4EF6\u5931\u8D25\uFF0C\u8BF7\u786E\u8BA4docId\u6709\u6548\uFF01
upload.artifactory.failed=\u4E0A\u4F20\u5236\u54C1\u5E93\u5931\u8D25! \u72B6\u6001\u7801 {0}
delete.artifactory.failed=\u5220\u9664\u5236\u54C1\u5E93\u6587\u4EF6\u5931\u8D25! \u72B6\u6001\u7801 {0}
download.artifactory.failed=\u4E0B\u8F7D\u5236\u54C1\u5E93\u6587\u4EF6\u5931\u8D25! \u72B6\u6001\u7801 {0}
soft.version.of.delete.data.is.not.the.latest=\u5F53\u524D\u7248\u672C {0} \u4E0D\u662F\u6700\u65B0\u7248\u672C,\u4E0D\u80FD\u5220\u9664
data.or.params.error=\u6570\u636E\u6216\u53C2\u6570\u5F02\u5E38! {0} \u4E3A\u7A7A 
match.task.no.data=\u6839\u636E\u4EFB\u52A1\u53F7\u548C\u6279\u6B21\u672A\u5339\u914D\u5230 , \u4EFB\u52A1\u53F7\uFF1A {0} , \u6279\u6B21\uFF1A {1}
emp.no.is.null=\u5DE5\u53F7\u4E0D\u80FD\u4E3A\u7A7A
The.page.size.should.not.be.larger.than.1000 = \u5206\u9875\u5C3A\u5BF8\u4E0D\u80FD\u5927\u4E8E1000
Do.not.perform.deletion.operation.without.incoming.data = \u672A\u4F20\u5165\u6570\u636E\u4E0D\u6267\u884C\u5220\u9664\u64CD\u4F5C
barcode.param.is.null.or.too.many=\u6761\u7801\u5143\u7D20\u4E0D\u5B58\u5728\u6216\u8FC7\u591A
classify.is.too.many=\u5B58\u5728\u591A\u4E2A\u5206\u7C7B\u56E0\u5B50
split.task.no.data=\u62C6\u5206\u7684\u4EFB\u52A1\u4E3A\u627E\u5230\uFF0C\u4EFB\u52A1\u53F7\uFF1A{0}\uFF0C\u6765\u6E90\uFF1A{1}
label.generate.rule.is.null=\u6807\u7B7E\u751F\u6210\u89C4\u5219\u4E0D\u80FD\u4E3A\u7A7A
match.multiple.label.generate.rule=\u5339\u914D\u5230\u591A\u6761\u6807\u7B7E\u751F\u6210\u89C4\u5219
generate.barcode.failure=\u751F\u6210\u6761\u7801\u5931\u8D25
generate.max.reel.id.count=reelIdCount\u4E0D\u80FD\u8D85\u8FC72000
task.not.exist.or.not.allow.to.modify=\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u8005\u53D1\u653E\u4E0D\u5141\u8BB8\u53D6\u6D88\uFF0C\u4EFB\u52A1\u53F7\uFF1A{0}
approval_log_name=\u5355\u677F\u62A5\u5E9F\u5BA1\u6279\u9519\u8BEF\u4FE1\u606F
bom_prod_params_errro=bomProd\u53C2\u6570\u5F02\u5E38
Hard.Cord.File.Template=\u6A21\u677F\u6587\u4EF6\u9519\u8BEF\uFF01\u672A\u5305\u542B\u5217\uFF1A
sn.is.null=\u6761\u7801\u4E3A\u7A7A
query.flag.error=\u67E5\u8BE2\u6807\u8BC6\u9519\u8BEF(1-CA\u4FE1\u606F,2-\u6821\u9A8C\u7801)
sn.num.is.more.than.100=\u67E5\u8BE2\u6761\u7801\u6570\u91CF\u8D85\u8FC7100
resource.use.info.export.max=\u4E00\u6B21\u6700\u591A\u53EA\u80FD\u52FE\u9009100\u4E2A\u8D44\u6E90\u7F16\u53F7\u7684\u4F7F\u7528\u8BB0\u5F55
ca.api.call.error=CA\u8BC1\u4E66\u63A5\u53E3\u8C03\u7528\u5F02\u5E38
item.no.need.bind.ca=\u8BE5\u7269\u6599\u65E0\u9700\u7ED1\u5B9ACA
task.data.is.error={0}\u6279\u6B21\u6570\u636E\u6709\u8BEF
no.task.need.to.gene=\u6CA1\u6709\u9700\u751F\u6210\u6761\u7801\u7684\u6279\u6B21
socket.io.exception=IO\u5F02\u5E38(loc:{0})
prodplan.id.is.null=\u6279\u6B21\u53F7\u4E3A\u7A7A
xml.to.obj.error=XML\u8F6C\u6362\u5BF9\u8C61\u5F02\u5E38: {0}
barcode.not.exist=\u539F\u6807\u7B7E\u3001\u65B0\u6807\u7B7E\u4E0D\u5B58\u5728
init.barcode.not.exist=\u539F\u6807\u7B7E\u4E0D\u5B58\u5728
new.barcode.existed=\u65B0\u6807\u7B7E\u5DF2\u6CE8\u518C
incomplete.conditions=\u6761\u4EF6\u4E0D\u5168
param.is.null=\u53C2\u6570\u7F3A\u5931
params.error=\u53C2\u6570\u5F02\u5E38:{0}
export.itemlistno.empty=\u5BFC\u51FA\u65F6\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
available.quantity.is.zero=\u53EF\u7528\u6570\u91CF\u4E3A0\uFF0C\u65E0\u6CD5\u7533\u8BF7\uFF0C\u8D44\u6E90\u6C60\u7F16\u53F7\uFF1A{0}
max.version.is.z=\u6700\u5927\u7248\u672C\u53F7\u4E3AZ
position.do.not.match.the.uploaded.record=\u5F53\u524D\u4F4D\u53F7 {0} \u548C\u5DF2\u4E0A\u4F20\u8BB0\u5F55\u7684\u4F4D\u53F7 {1} \u4E0D\u5B8C\u5168\u5339\u914D\u3002\u8BF7\u68C0\u67E5
factory.id.or.emp.no.is.null=\u5DE5\u5382id\u6216\u7528\u6237\u7F16\u53F7\u4E3A\u7A7A
data.not.exist.or.has.deleted=\u6570\u636E\u4E0D\u5B58\u5728\u6216\u5DF2\u5220\u9664
apply.qty.exceed.can.use.qty=\u7533\u8BF7\u6570\u91CF\u5927\u4E8E\u53EF\u7528\u6570\u91CF, \u53EF\u7528\u6570\u91CF:{0}
board.instruction.cycle.info.is.creating.please.wait=\u5355\u677F\u6307\u4EE4\u5468\u671F\u4FE1\u606F\u6570\u636E\u6B63\u5728\u751F\u6210\u4E2D\u3002\u8BF7\u7A0D\u5019\u91CD\u8BD5
network.license.sign.lock=\u5165\u7F51\u8D44\u6E90\u6B63\u5728\u5BFC\u5165\u4E2D\uFF0C\u8BF7\u7A0D\u5019\u91CD\u8BD5
network.license.resource.not.fount=\u8D44\u6E90\u53F7\u4E0D\u5B58\u5728\uFF01
network.license.print.not.fount=\u8D44\u6E90\u53F7\u6216SN\u6253\u5370\u8BB0\u5F55\u4E0D\u5B58\u5728
network.license.resource.print.not.fount={0}\u8D44\u6E90\u53F7\u6253\u5370\u8BB0\u5F55\u4E0D\u5B58\u5728\uFF01
reel.id.not.register=ReelId\u672A\u6CE8\u518C
work.time.error=\u5B50\u5DE5\u5E8F{0}\u4F5C\u4E1A\u65F6\u957F\u53EA\u80FD\u662F1-2000\u7684\u6B63\u6574\u6570
remain.time.error=\u5B50\u5DE5\u5E8F{0}\u6EDE\u7559\u65F6\u957F\u53EA\u80FD\u662F1-2000\u7684\u6B63\u6574\u6570
last.process.should.be.stock=\u5DE5\u827A\u8DEF\u5F84\u4E2D\u6700\u540E\u4E00\u4E2A\u5B50\u5DE5\u5E8F\u5FC5\u987B\u662F\u201C\u5165\u5E93\u201D
userType.not.corrent=\u7528\u6237\u7C7B\u578B\u4E0D\u6B63\u786E
rfid.sign.info.data.is.null=\u7B7E\u540D\u6570\u636E\u6CA1\u6709\u4F20
rfid.sign.info.not.setting=\u6CA1\u6709\u8BBE\u7F6E\u7535\u5B50\u7B7E\u540D\u4FE1\u606F
rfid.sign.info.not.salt=\u6CA1\u6709\u8BBE\u7F6E\u7535\u5B50\u7B7E\u540D\u52A0\u5BC6\u76D0\u503C
rfid.sign.info.not.cert.id=\u6CA1\u6709\u8BBE\u7F6E\u7535\u5B50\u7B7E\u540D\u8BC1\u4E66id
rfid.sign.info.not.cert.id.get.addr=\u6CA1\u6709\u8BBE\u7F6E\u7535\u5B50\u7B7E\u540D\u8BC1\u4E66id\u83B7\u53D6\u5730\u5740
rfid.sign.info.cert.id.get.fail=\u8BC1\u4E66id\u83B7\u53D6\u5931\u8D25
rfid.sign.info.cert.id.get.error=\u8BC1\u4E66id\u83B7\u53D6\u53D1\u751F\u5F02\u5E38
rfid.sign.info.cert.id.oper.success=\u8BC1\u4E66id\u83B7\u53D6\u548C\u66F4\u65B0\u6210\u529F
resource.application.is.applying=\u8D44\u6E90\u7533\u8BF7\u6B63\u5728\u8FDB\u884C\u4E2D, \u8D44\u6E90\u7F16\u53F7\uFF1A{0}
resource.application.not.exist=\u8D44\u6E90\u7533\u8BF7\u4E0D\u5B58\u5728, \u8D44\u6E90\u7533\u8BF7ID\uFF1A{0}
resource.application.apply.amount.is.null=\u8D44\u6E90\u7533\u8BF7\u6570\u91CF\u4E3A\u7A7A, \u7533\u8BF7\u5355\u53F7\uFF1A{0}
resource.application.not.newest=\u8BE5\u8D44\u6E90\u7533\u8BF7\u4E0D\u6700\u65B0\u7533\u8BF7\u7684\u8D44\u6E90, \u65E0\u6CD5\u4F5C\u5E9F
board.first.house.service.error=\u5355\u677F\u9996\u4EF6\u5165\u5E93\u670D\u52A1\u51FA\u9519
prodplan.service.error=\u751F\u4EA7\u6307\u4EE4\u670D\u52A1\u51FA\u9519
re.work.error=\u8FD4\u5DE5\u626B\u63CF\u67E5\u4EA7\u54C1\u5927\u7C7B\uFF0C\u5165\u53C2\u4E3A\u7A7A
resource.info.not.exist=\u8D44\u6E90\u6C60\u4FE1\u606F\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\uFF0C\u8D44\u6E90\u7F16\u53F7\uFF1A{0}
date.error=\u65E5\u671F\u7C7B\u578B\u9519\u8BEF
at.least.choose.one=\u67E5\u8BE2\u6761\u4EF6\u4EA4\u4ED8\u96C6\u3001\u5408\u540C\u53F7\u3001\u65F6\u95F4\u81F3\u5C11\u9009\u62E9\u4E00\u4E2A
datatype.error=\u6570\u636E\u7C7B\u578B\u9519\u8BEF
datetype.error=\u65E5\u671F\u7C7B\u578B\u9519\u8BEF
cycle.type.error=\u5FAA\u73AF\u7C7B\u578B\u9519\u8BEF
result.type.error=\u8F93\u51FA\u7C7B\u578B\u9519\u8BEF
deliver.sets.service.error=\u70B9\u5BF9\u70B9\u5FAE\u670D\u52A1\u9519\u8BEF
cycle.productclass.and.plangroup.error=\u4EA7\u54C1\u5927\u7C7B\u548C\u8BA1\u5212\u7EC4\u4E0D\u80FD\u540C\u65F6\u4F5C\u4E3A\u67E5\u8BE2\u6761\u4EF6
str.format.time.not.null=\u65F6\u95F4\u5B57\u7B26\u4E32\u4E0D\u80FD\u4E3A\u7A7A,\u5B57\u7B26\u4E32\u683C\u5F0F\u5FC5\u987B\u4E3A(yyyy-MM-dd HH:mm:ss), \u4F8B: 1999-01-01 00:00:00
no.data.to.export=\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA
deliver.entity.service.error=\u4EFB\u52A1\u751F\u4EA7\u5468\u671F\u51FA\u9519
bom.server.error=\u5355\u677F\u751F\u4EA7\u5468\u671F\u670D\u52A1\u51FA\u9519
afferent.data.exceeds.maximum=\u4F20\u5165\u6570\u636E\u8D85\u51FA\u6700\u5927\u503C
no.maximum.value.found=\u6CA1\u6709\u627E\u5230\u8BBE\u7F6E\u7684\u6700\u5927\u503C
data.is.empty=\u6570\u636E\u4E3A\u7A7A;
received.data.not.meet.requirements=\u63A5\u6536\u5230\u7684\u6570\u636E\u4E0D\u7B26\u5408\u8981\u6C42\uFF0C\u8BF7\u91CD\u65B0\u786E\u8BA4\u540E\u63D0\u4EA4
check.the.file.and.the.selected.customer.part.type=\u8BF7\u68C0\u67E5\u6587\u4EF6\u548C\u6240\u9009\u5BA2\u6237\u90E8\u4EF6\u7C7B\u578B\u662F\u5426\u4E00\u81F4
received.data.not.meet.requirements.new=\u63A5\u6536\u5230\u7684\u6570\u636E\u4E0D\u7B26\u5408\u8981\u6C42\uFF0C\u8BF7\u91CD\u65B0\u786E\u8BA4\u540E\u63D0\u4EA4 {0}
file_format_error=\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u4F7F\u7528excel\u5BFC\u5165\uFF01
workbook_init_fail=Workbook\u521D\u59CB\u5316\u5931\u8D25
import.failed.please.try.again=\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01
import.successful.rows=\u5BFC\u5165\u6210\u529F\uFF01\u884C\u6570\uFF1A
max.import.is.once=\u4E00\u6B21\u6700\u591A\u53EA\u80FD\u5BFC\u51655000\u6761\uFF01
subcontractor.id.can.not.empty=\u5916\u534F\u5382ID\u4E0D\u5141\u8BB8\u4E3A\u7A7A\uFF0C\u7B2C\uFF1A {0} \u884C\u3002
access.party.code.is.empty=\u63A5\u5165\u65B9\u7F16\u7801\u4E3A\u7A7A\uFF01
outsourcing.factory.name.is.empty=\u5916\u534F\u5382\u540D\u79F0\u4E3A\u7A7A\uFF01
zte.po.is.empty=\u4E2D\u5174po\u4E3A\u7A7A\uFF01
outsourcing.work.order.number.is.empty=\u5916\u534F\u5DE5\u5355\u53F7\u4E3A\u7A7A\uFF01
device.sn.en.product.empty=\u6574\u673Asn/en/\u4EA7\u54C1/sn\u4E3A\u7A7A\uFF01
sn.is.empty=\u5355\u677Fsn\u4E3A\u7A7A\uFF01
mac.is.empty=mac\u4E3A\u7A7A
product.code.is.empty=\u4EA7\u54C1\u4EE3\u7801\u4E3A\u7A7A\uFF01
product.code.not.exist=\u7269\u6599\u4EE3\u7801\u4E0D\u5B58\u5728\uFF01
primary.key.is.empty=\u4E3B\u952E\u4E3A\u7A7A
system.not.support.the.type=\u7CFB\u7EDF\u6682\u4E0D\u652F\u6301\u6B64\u79CD\u4E0A\u9001\u7C7B\u578B\u6570\u636E
tk.registration.info.not.found=\u627E\u4E0D\u5230\u63A5\u5165\u65B9\u6CE8\u518C\u4FE1\u606F(tk)\u3002
pk.registration.info.not.found=\u627E\u4E0D\u5230\u63A5\u5165\u65B9\u6CE8\u518C\u4FE1\u606F(pk)\u3002
submited.data.is.invalid=\u4E0A\u9001\u6570\u636E\u65E0\u6548\uFF0C\u9A8C\u7B7E\u5931\u8D25\u3002
submited.data.format.error=\u4E0A\u9001\u6570\u636E\u683C\u5F0F\u9519\u8BEF\uFF0C\u6CA1\u6709\u4F20\u9012\u503C\uFF1A
import.failed.please.check.data=\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u8868\u4E2D\u6570\u636E\u540E\u91CD\u8BD5\uFF01
product.library.cannot.be.empty=\u4EA7\u54C1\u5E93\u5305\u7ED3\u5B58\u4E0D\u5141\u8BB8\u4E3A\u7A7A\uFF0C\u7B2C\uFF1A
plan.quantity.cannot.be.empty=\u8BA1\u5212\u6570\u91CF\u4E0D\u5141\u8BB8\u4E3A\u7A7A
product.categories.cannot.be.empty=\u4EA7\u54C1\u5927\u7C7B\u4E0D\u5141\u8BB8\u4E3A\u7A7A
product.model.cannot.be.empty=\u4EA7\u54C1\u578B\u53F7\u4E0D\u5141\u8BB8\u4E3A\u7A7A
product.name.cannot.be.empty=\u4EA7\u54C1\u540D\u79F0\u4E0D\u5141\u8BB8\u4E3A\u7A7A
shipped.number.cannot.be.empty=\u5DF2\u53D1\u8D27\u6570\u4E0D\u5141\u8BB8\u4E3A\u7A7A
deadline.cannot.be.empty=\u622A\u6B62\u65F6\u95F4\u4E0D\u5141\u8BB8\u4E3A\u7A7A
board.balance.cannot.be.empty=\u5355\u677F\u7ED3\u5B58\u4E0D\u5141\u8BB8\u4E3A\u7A7A
test.package.balance.cannot.be.empty=\u6D4B\u8BD5\u5305\u7ED3\u5B58\u4E0D\u5141\u8BB8\u4E3A\u7A7A
vendor.cannot.be.empty=\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A
item.name.cannot.be.empty=\u7269\u6599\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
item.model.cannot.be.empty=\u7269\u6599\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
material.po.number.cannot.be.empty=\u7269\u6599PO\u53F7\u4E0D\u80FD\u4E3A\u7A7A
this.batch.quantity.cannot.be.empty=\u672C\u6279\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
sampling.quantity.cannot.be.empty=\u62BD\u68C0\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
incoming.date.cannot.be.empty=\u6765\u6599\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
detection.date.cannot.be.empty=\u68C0\u6D4B\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
detection.no.cannot.be.empty=\u68C0\u6D4B\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
sampling.method.cannot.be.empty=\u62BD\u6837\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A
detection.result.is.empty=\u68C0\u6D4B\u7ED3\u679C\u4E3A\u7A7A
item.code.cannot.be.empty=\u7269\u6599\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
item.no.cannot.be.empty=\u7269\u6599\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
outsourcing.reelid.is.empty=\u5916\u534F REEL ID\u4E3A\u7A7A
reelid.quantity.is.empty=REEL ID\u6570\u91CF\u4E3A\u7A7A
correlation.time.is.empty=\u5173\u8054\u65F6\u95F4\u4E3A\u7A7A
stock.quantity.is.empty=\u5E93\u5B58\u6570\u91CF\u4E3A\u7A7A\uFF01
stock.cutoff.time.is.empty=\u5E93\u5B58\u622A\u6B62\u65F6\u95F4\u4E3A\u7A7A\uFF01
line_can_not_be_empty=\u7EBF\u4F53\u4E0D\u5141\u8BB8\u4E3A\u7A7A
sn.scan.time.cannot.be.empty=SN\u626B\u63CF\u65F6\u95F4\u4E0D\u5141\u8BB8\u4E3A\u7A7A\uFF0C\u6216\u8005\u683C\u5F0F\u9519\u8BEF
feed.quantity.cannot.be.empty=\u4E0A\u6599\u6570\u91CF\u4E0D\u5141\u8BB8\u4E3A\u7A7A
feed_time_cannot_be_empty=FEED_TIME\u4E0D\u5141\u8BB8\u4E3A\u7A7A\uFF0C\u6216\u8005\u683C\u5F0F\u9519\u8BEF
file_name_is_empty=\u6587\u4EF6\u540D\u4E3A\u7A7A
data.table.duplicated=\u6570\u636E\u8868\u4E2D\u6570\u636E\u91CD\u590D\uFF01
license.plate.number.is.empty=\u8F66\u724C\u53F7\u4E3A\u7A7A\uFF01
shuttle.error=\u73ED\u8F66ERROR
phone.number.is.empty=\u624B\u673A\u53F7\u7801\u4E3A\u7A7A\uFF01
route.is.empty=\u8DEF\u7EBF\u4E3A\u7A7A\uFF01
tag.sequence.not.exists=\u8BE5\u6807\u7B7E\u4E0D\u5B58\u5728\u5E8F\u5217
no.bom.details.data=\u65E0BOM\u660E\u7EC6\u6570\u636E
material.base.attributes.not.found=\u672A\u67E5\u8BE2\u5230\u7269\u6599\u57FA\u7840\u5C5E\u6027
tag.number.analysis.failed=\u4F4D\u53F7\u5206\u6790\u5931\u8D25
not.meet.rule=\u4E0D\u6EE1\u8DB3\u72EC\u7ACB\u53F7\u89C4\u5219,
exist.multiple.connectors=\u5B58\u5728\u591A\u4E2A\u8FDE\u63A5\u7B26,
not.meet.consecutive.number.rule=\u4E0D\u6EE1\u8DB3\u8FDE\u7EED\u53F7\u89C4\u5219,
two.tag.numbers.characters.not.equal=\u4E24\u4E2A\u4F4D\u53F7\u6570\u5B57\u524D\u5B57\u7B26\u4E0D\u76F8\u7B49,
start.num.greater.or.equal.end.num=\u5F00\u59CB\u4F4D\u53F7\u5927\u4E8E\u6216\u7B49\u4E8E\u7ED3\u675F\u4F4D\u53F7,
no.bom.data.need.split=\u6CA1\u6709\u9700\u62C6\u5206\u7684BOM\u6570\u636E
tag.number.split.failed=\u4F4D\u53F7\u62C6\u5206\u5931\u8D25
file.parsing.error=\u6587\u4EF6\u89E3\u6790\u9519\u8BEF
file.parsing.success=\u6587\u4EF6\u89E3\u6790\u6210\u529F
sn.rule.params.error=\u6761\u7801\u89C4\u5219\u53C2\u6570\u5F02\u5E38
product.classification.cannot.be.empty=\u4EA7\u54C1\u5206\u7C7B\u4E0D\u5141\u8BB8\u4E3A\u7A7A
exist.in.other.categories=\u5728\u5176\u4ED6\u5927\u7C7B\u4E2D\u5B58\u5728
exist.in.other.categories.new={0}\u5728\u5176\u4ED6\u5927\u7C7B\u4E2D\u5B58\u5728
existed=\u5DF2\u5B58\u5728
description=\u63CF\u8FF0\uFF1A
description.new=\u63CF\u8FF0\uFF1A{0}
name=\u540D\u79F0\uFF1A
name.new=\u540D\u79F0\uFF1A{0}
product.unit.cannot.be.empty=\u4EA7\u54C1\u5355\u4F4D\u4E0D\u80FD\u4E3A\u7A7A
incoming.model.is.empty=\u4F20\u5165\u673A\u578B\u4E3A\u7A7A
classification.exist.storage.value=\u5F53\u524D\u5206\u7C7B\u5DF2\u6709\u5165\u5E93\u76EE\u6807\u503C\uFF0C\u65E0\u6CD5\u5220\u9664\u6216\u4FEE\u6539
model.already.exists=\u673A\u578B\u5DF2\u5B58\u5728
click.the.download.address.below=\u70B9\u51FB\u4E0B\u9762\u7684\u4E0B\u8F7D\u5730\u5740\uFF08\u4E0B\u8F7D\u5BFC\u51FA\u6587\u4EF6\uFF09
download.address='>\u4E0B\u8F7D\u5730\u5740(
delete.automatically.after=\u540E\u81EA\u52A8\u5220\u9664,\u8BF7\u5C3D\u5FEB\u4E0B\u8F7D</p>
barcode.create.excel.success=\u6761\u7801\u8FFD\u6EAF\u4FE1\u606F\u67E5\u8BE2\uFF0C\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u6210\u529F
address.will.be.at=<p style='color:red'>\u6B64\u5730\u5740\u5C06\u5728
query.result.is.empty=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
create.query.result.excel.failed=\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u5931\u8D25
generate.query.result.excel.failed=\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u5931\u8D25\uFF1A\u8BF7\u91CD\u65B0\u751F\u6210
item.code.prefix.cannot.be.empty=\u7269\u6599\u4EE3\u7801\u524D\u7F00\u4E0D\u80FD\u4E3A\u7A7A
material.code.prefix.length.be=,\u7269\u6599\u4EE3\u7801\u524D\u7F00\u957F\u5EA6\u5FC5\u987B\u4E3A
product.type.length.cannot.exceed=\u4EA7\u54C1\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7
mac.address.not.maintained=MAC1\u5730\u5740\uFF1A{0\u672A\u5728MAC\u5730\u5740\u533A\u95F4\u7EF4\u62A4\u91CC,\u4E0D\u80FD\u5BFC\u5165}
database.table.exist.sn=\u6570\u636E\u5E93\u4E2D\u7684\u8868\u5DF2\u7ECF\u5B58\u5728\uFF1A{0}\u5B50\u90E8\u4EF6\u6761\u7801
date.format.must.be=\u751F\u4EA7\u65E5\u671F\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A\u4E14\u683C\u5F0F \u5FC5\u987B\u4E3A\uFF1Ayyyy-MM-dd\u6216yyyy/MM/dd!\u884C\uFF1A
some.parms.cannot.be.empty=\u5185\u90E8\u5408\u540C\u53F7\u3001\u5B50\u90E8\u4EF6\u4EE3\u7801\u3001\u5B50\u90E8\u4EF6\u6761\u7801\u3001\u751F\u4EA7\u65E5\u671F\u3001\u8F6F\u4EF6\u7248\u672C\u3001\u786C\u4EF6\u7248\u672C\u3001MAC1\u3001\u5916\u7BB1\u7BB1\u53F7\u3001\u63A5\u5165\u5730\u5740\u3001\u7528\u6237\u540D\u3001\u7EC8\u7AEF\u914D\u7F6E\u5BC6\u7801\u3001\u5236\u9020\u5DE5\u827A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF01\u884C\uFF1A
data.is.error=\u6570\u636E\u5F02\u5E38,\u8BF7\u91CD\u65B0\u5BFC\u5165:
mac.address.invalid=\u8BF7\u8F93\u5165\u6709\u6548\u7684MAC\u5730\u5740
duplicate.bar.code.inside.template=\u6A21\u677F\u5185\u90E8\u5B50\u90E8\u4EF6\u6761\u7801\u91CD\u590D
duplicate.gpon.sn.inside.the.template=\u6A21\u677F\u5185\u90E8GPON-SN\u91CD\u590D
duplicate.device.id.inside.template=\u6A21\u677F\u5185\u90E8\u8BBE\u5907\u6807\u8BC6\u91CD\u590D
create.query.result.excel.success=\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u6210\u529F
records.qty.cannot.exceed=\u8BB0\u5F55\u6570\u4E0D\u5141\u8BB8\u8D85\u8FC7
production.unit.does.not.exist=\u751F\u4EA7\u5355\u4F4D\u4E0D\u5B58\u5728
mac.start.address.less.end=MAC\u8D77\u59CB\u5730\u5740\u5E94\u5C0F\u4E8E\u7ED3\u675F\u5730\u5740,\u4E14\u5730\u5740\u533A\u95F4\u4E0D\u80FD\u5927\u4E8E200\u4E07\uFF0C\u4E0D\u80FD\u65B0\u589E
mac.address.cannot.be.duplicated=MAC\u5730\u5740\u4E0D\u80FD\u91CD\u590D
failed.to.get.user.email=\u83B7\u53D6\u7528\u6237email\u5931\u8D25
email.format.incorrect=email\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u8BF7\u786E\u8BA4
email.to.is.empty=email\u6536\u4EF6\u4EBA\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
email.send.err=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25:{0}
failed.to.get.redis.lock=\u83B7\u53D6redis\u9501\u8D44\u6E90\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5
failed.to.get.registration.info=\u5F85\u6CE8\u518C\u4FE1\u606F\u83B7\u53D6\u5931\u8D25
import.data.is.empty=\u5BFC\u5165\u6570\u636E\u4E3A\u7A7A
duplicate.sn=SN\u91CD\u590D
duplicate.sn.new=SN\u91CD\u590D {0}
brand.cannot.be.empty=Brand/\u54C1\u724C\u4E0D\u80FD\u4E3A\u7A7A
device.type.cannot.be.empty=Type/\u8BBE\u5907\u79CD\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
model.cannot.be.empty=Model/\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
prod.task.no.cannot.be.empty=\u751F\u4EA7\u4EFB\u52A1\u53F7(\u5916\u534F)\u4E0D\u80FD\u4E3A\u7A7A
manufacturing.process.no.cannot.be.empty=Product BOM/\u5236\u9020\u5DE5\u827A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
mac1.cannot.be.empty=STB MAC1/ MAC1* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
sn.cannot.be.empty=PCBA SN1/\u5355\u677FSN1* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
product.sn.cannot.be.empty=STB SN/\u4EA7\u54C1SN* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
carton.sn.cannot.be.empty=CARTOON SN/\u7EB8\u7BB1SN* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
params.cannot.be.empty=\u6808\u677FSN/\u751F\u4EA7\u65E5\u671F/\u7535\u6E90SN/\u9065\u63A7\u5668SN/FT\u6D4B\u8BD5\u7ED3\u679C/FT\u6D4B\u8BD5\u65F6\u95F4/Burn-In Result\u8001\u5316\u6D4B\u8BD5\u7ED3\u679C/Burn-In Time\u8001\u5316\u6D4B\u8BD5\u65F6\u957F/MAC\u914D\u7F6E\u7ED3\u679C/MAC\u914D\u7F6E\u65F6\u95F4* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
part.two.params.cannot.be.empty=\u5F00\u5173\u673A\u6D4B\u8BD5\u7ED3\u679C/\u5F00\u5173\u673A\u6D4B\u8BD5\u65F6\u95F4/\u6574\u673A\u6D4B\u8BD5\u7ED3\u679C/\u6574\u673A\u6D4B\u8BD5\u65F6\u95F4/\u6574\u673A\u6821\u9A8C\u7ED3\u679C/\u6574\u673A\u6821\u9A8C\u65F6\u95F4* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
part.three.params.cannot.be.empty=\u51FA\u5382\u914D\u7F6E\u7ED3\u679C/\u51FA\u5382\u914D\u7F6E\u65F6\u95F4/\u8F6F\u4EF6\u7248\u672C\u53F7/Logo\u7248\u672C\u53F7/\u51FA\u5382\u914D\u7F6E\u6587\u4EF6\u540D* \u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A
product.sn.already.exists=\u4EA7\u54C1SN\u5DF2\u7ECF\u5B58\u5728
transmission.check.failed=\u4F20\u8F93\u6821\u9A8C\u5931\u8D25(SHA256)
no.barcode.required.bind.ca.certificate=\u6CA1\u6709\u9700\u8981\u7ED1\u5B9ACA\u8BC1\u4E66\u7684\u6761\u7801
file.upload.failed=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
generate.key.exception=\u751F\u6210\u79D8\u94A5\u5F02\u5E38
encryption.exception=\u52A0\u5BC6\u5F02\u5E38
decryption.exception=\u89E3\u5BC6\u5F02\u5E38
signature.exception=\u7B7E\u540D\u5F02\u5E38
private.key.is.empty=\u79C1\u94A5\u4E3A\u7A7A
signing.exception=\u9A8C\u7B7E\u5F02\u5E38
file.initialization.failed=\u6587\u4EF6\u521D\u59CB\u5316\u5931\u8D25
file_is_null=\u6587\u4EF6\u4E3A\u7A7A
data.error=\u6570\u636E\u6709\u8BEF
device.is.bound=\u8BBE\u5907\u5DF2\u7ED1\u5B9A\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9
insert.data.operation.failed=\u63D2\u5165\u6570\u636E\u64CD\u4F5C\u5931\u8D25\uFF01
card.machine.info.is.existed=\u8BE5\u5361\u673A\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
del.data.operation.failed=\u5220\u9664\u6570\u636E\u64CD\u4F5C\u5931\u8D25\uFF01
update.data.operation.failed=\u4FEE\u6539\u6570\u636E\u64CD\u4F5C\u5931\u8D25\uFF01
departmenet.is.existed=\u90E8\u95E8\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
employee.no.is.existed=\u8BE5\u5DE5\u53F7\u5DF2\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
card.no.is.existed=\u8BE5\u5361\u53F7\u5DF2\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
import.successful=\u5BFC\u5165\u6210\u529F!
scheduled.update.record.execution.failed=\u5B9A\u65F6\u66F4\u65B0\u8BB0\u5F55\u6267\u884C\u5931\u8D25 
interface.call.succeeded=\u63A5\u53E3\u8C03\u7528\u6210\u529F
interface.call.error=\u63A5\u53E3\u8C03\u7528\u5F02\u5E38:{0}
is.creating.please.wait=\u5408\u540C\u4EFB\u52A1\u8868\u5F81\u6570\u636E\u6B63\u5728\u751F\u6210\u4E2D\u3002\u8BF7\u7A0D\u5019\u91CD\u8BD5
file.upload.succeed=\u6587\u4EF6\u4E0A\u4F20\u6210\u529F
duplicate.route.name=\u8DEF\u7EBF\u540D\u79F0\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u547D\u540D\uFF01
miss.scheduled.date=\u7B2C{0}\u6761\u7F3A\u5931\u8C03\u5EA6\u65E5\u671F\uFF01\u8BF7\u68C0\u67E5
duplicate.site.name=\u7AD9\u70B9\u540D\u79F0\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\u7AD9\u70B9\u540D\u79F0\uFF01
stop.already.existed=\u7ECF\u505C\u7AD9\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\uFF01
serial.number.is.exists=\u5E8F\u53F7\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
sn.generation.failed=\u6761\u7801\u751F\u6210\u5931\u8D25
sn.generation.success=\u6761\u7801\u751F\u6210\u6210\u529F
sn.bind.ca.failed=\u6761\u7801\u7ED1\u5B9ACA\u5931\u8D25
access.no.is.existed=\u63A5\u5165\u65B9\u7F16\u53F7\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
access.name.is.existed=\u63A5\u5165\u65B9\u540D\u79F0\u5DF2\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
bind_success=\u7ED1\u5B9A\u6210\u529F
operation.success=\u64CD\u4F5C\u6210\u529F
preProcess.destination.code.existed=\u524D\u52A0\u5DE5\u53BB\u5411\u4EE3\u7801\u5DF2\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
generating.excel=\u6B63\u5728\u751F\u6210excel\uFF0C\u8BF7\u7559\u610F\u90AE\u4EF6
generating.offline.export.excel=\u6B63\u5728\u751F\u6210excel\uFF0C\u8BF7\u67E5\u770B\u4E0B\u8F7D\u4E2D\u5FC3
processing.failed=\u5904\u7406\u5931\u8D25
operator.cannot.be.empty=\u64CD\u4F5C\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
no.data.to.del=\u6CA1\u6709\u53EF\u5220\u9664\u6570\u636E\uFF0C\u53EA\u80FD\u5220\u9664\u81EA\u5DF1\u5BFC\u5165\u7684\u6570\u636E\uFF01
mac.format.incorrect=MAC\u683C\u5F0F\u4E0D\u6B63\u786E\uFF01
failed.export.emp.no.is.empty=\u5BFC\u51FA\u6570\u636E\u5931\u8D25\uFF0C\u7528\u6237empNo\u4E3A\u7A7A
reel.id.verify.passed=Reel ID\u6821\u9A8C\u901A\u8FC7
reel.id.is.wrong=Reel ID\u6709\u8BEF
abnormal.operation=\u64CD\u4F5C\u5F02\u5E38
reelid.is.registered=ReelId\u5DF2\u6CE8\u518C
number.cannot.exceed=\u8BB0\u5F55\u6570\u4E0D\u5141\u8BB8\u8D85\u8FC75000\u884C
catalog.code.cannot.be.empty=\u8BF7\u8F93\u5165\u76EE\u5F55\u4EE3\u7801
sub.item.code.can.not.be.empty=\u8BF7\u8F93\u5165\u5B50\u9879\u4EE3\u7801
authentication.failed={"message": "Unified authorization authentication failed"}
max.print.count.is.2000=\u6253\u5370\u6570\u91CF\u4E0D\u80FD\u5C0F\u4E8E0\u4E14\u4E0D\u80FD\u5927\u4E8E2000
barcode.generate.error=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u751F\u6210\u6761\u7801\u5F02\u5E38! {0}
barcode.call.system=\u8C03\u7528\u7CFB\u7EDF\u4E0D\u80FD\u4E3A\u7A7A
barcode.is.null=\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
choreographer.url.type.error=\u7F16\u6392\u5668\u63A5\u53E3\u5730\u5740\u6570\u636E\u5B57\u5178\u672A\u914D\u7F6E
choreographer.call.error=\u7F16\u6392\u5668\u63A5\u53E3\u8C03\u7528\u51FA\u9519
taskNo.is.null=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A
get.itemlistno.error=\u6839\u636E\u6279\u6B21\u67E5\u8BE2\u6599\u5355\u4EE3\u7801\u51FA\u9519
query.style.and.brand.error=\u56DE\u5199\u67E5\u8BE2\u578B\u53F7\u62A5\u9519
query.export.error=\u8D44\u6E90\u6C60\u7EF4\u62A4\u5BFC\u51FA\u5F02\u5E38\u6570\u636E\u5B57\u5178\u672A\u914D\u7F6E
avl.query.error=AVL\u67E5\u8BE2\u5931\u8D25
reelid.null=reelid\u4E3A\u7A7A
item.no.null=\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A
product.task.is.null=\u751F\u4EA7\u6279\u6B21\u4E3A\u7A7A
reelid.sn.is.null=220\u6761\u7801\u4E3A\u7A7A
pkcode.info.is.empty=\u67E5\u8BE2pkCode\u4FE1\u606F\u4E3A\u7A7A
input.more.than.regiter=\u8F93\u5165\u6570\u91CF\u5927\u4E8E\u5F85\u6CE8\u518C\u6570\u91CF
blank.generate.error=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u751F\u6210\u7A7A\u6761\u7801\u5931\u8D25 {0}
please.update.printcs=token\u4E3A\u7A7A\u3002\u8BF7\u66F4\u65B0\u6253\u5370\u7A0B\u5E8F
taskStatus.not.allow.to.modify=\u5F53\u524D\u4EFB\u52A1\u72B6\u6001\u4E0D\u5141\u8BB8\u4FEE\u6539\uFF0C\u4EFB\u52A1\u53F7\u662F\uFF1A{0}
not.lfid.meter=\u4E0D\u662F {0} \u7684\u7269\u6599
reel.id.registered={0} Reel Id \u5DF2\u6CE8\u518C
printType.or.printScene.is.null=\u6253\u5370\u7C7B\u578B\u6216\u6253\u5370\u573A\u666F\u4E3A\u7A7A
not.find=\u672A\u53D1\u73B0\u4E0A\u4F20\u6587\u4EF6
material.file.upload.error=\u7269\u6599\u7EF4\u62A4\u4E0A\u4F20\u6587\u4EF6\u89E3\u6790\u9519\u8BEF.
material.file.upload.empty=\u89E3\u6790excel \u8868\u683C\u6570\u636E\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5\u683C\u5F0F\u3002
material.file.upload.max.beyond=\u5BFC\u5165excle\u6761\u76EE\u7684\u6700\u5927\u6570\u91CF\u4E3A\uFF1A {0} \u8BF7\u4FEE\u6539\u540E\u5206\u6279\uFF0C\u91CD\u65B0\u4E0A\u4F20\u6587\u4EF6\u3002
material.file.upload.message=\u5BFC\u5165excle\u6210\u529F\u6761\u6570\uFF1A{0}\uFF0C\u5931\u8D25\u6761\u6570\uFF1A{1}\u3002
tagNum.null.is.create=\u8BE5\u7269\u6599\u4EE3\u7801\u7EF4\u62A4\u4E86\u4F4D\u53F7\u4E3A\u7A7A\u7684\u524D\u52A0\u5DE5\uFF0C\u4E0D\u80FD\u518D\u7EF4\u62A4\u6709\u4F4D\u53F7\u7684\u524D\u52A0\u5DE5\u3002
tagNum.null.can.not.create=\u8BE5\u7269\u6599\u4EE3\u7801\u7EF4\u62A4\u4E86\u524D\u52A0\u5DE5\u4F4D\u53F7\u4E0D\u4E3A\u7A7A\u7684\uFF0C\u4E0D\u80FD\u518D\u7EF4\u62A4\u4F4D\u53F7\u4E3A\u7A7A\u7684\u524D\u52A0\u5DE5\u3002
material.code.has.maintained.the.pre.processing.data=\u8BE5\u7269\u6599\u4EE3\u7801\u7EF4\u62A4\u4E86\u524D\u52A0\u5DE5\u6570\u636E
material.code.has.maintained.only.distribution.operation=\u8BE5\u7269\u6599\u4EE3\u7801\u7EF4\u62A4\u4E86\u53EA\u6709\u914D\u9001\u5DE5\u5E8F{0}\u7684\u524D\u52A0\u5DE5\u6570\u636E
tagnum.distribution.operation.inconsistent=\u5F53\u524D\u9009\u62E9\u7684\u914D\u9001\u5DE5\u5E8F\u4E0E\u7EF4\u62A4\u914D\u9001\u5DE5\u5E8F:{0}\u4E0D\u4E00\u81F4,\u8BF7\u786E\u8BA4\uFF01
distribution.operation.is.empty=\u914D\u9001\u5DE5\u5E8F\u4E3A\u7A7A
tagNum.exist.pre.processing.type.and.destination.is.null=\u4F4D\u53F7\u4E0D\u4E3A\u7A7A\u65F6\u524D\u52A0\u5DE5\u7C7B\u578B\u4EE5\u53CA\u53BB\u5411\u4E0D\u80FD\u4E3A\u7A7A
add.pre.manu.info.batch.failed=\u65B0\u589E\u524D\u52A0\u5DE5\u4FE1\u606F\u5931\u8D25\uFF0C\u9519\u8BEF\u4FE1\u606F:{0}
reelid.not.exist.in.mes=\u539FREELID\u5728IMES\u7CFB\u7EDF\u4E0D\u5B58\u5728\u3002
tagNum.exist.disabe.create=\u5DF2\u7ECF\u7EF4\u62A4\u4F4D\u53F7\u4E3A\u7A7A\u7684\u524D\u52A0\u5DE5\u6570\u636E\u3002
tagnum.is.exit=\u7EF4\u62A4\u4E86\u4F4D\u53F7 {0} \u7684 {1} \u524D\u52A0\u5DE5\uFF0C\u4E0D\u80FD\u518D\u7EF4\u62A4\u3002
tagnum.some.is.exit=\u4F4D\u53F7{0}\u4E2D\u5305\u542B\u5DF2\u7ECF\u7EF4\u62A4\u4F4D\u53F7 {1} \u4FE1\u606F\uFF0C\u5FC5\u987B\u4FDD\u6301\u4F4D\u53F7\u4FE1\u606F\u4E00\u81F4\u3002
generate.apply.no.failed=\u751F\u6210\u5355\u636E\u53F7\u5931\u8D25\uFF01
get.program.bill.info.failed=\u83B7\u53D6\u70E7\u5F55\u9A8C\u8BC1\u5355\u4FE1\u606F\u5931\u8D25\uFF01
get.program.sample.info.failed=\u83B7\u53D6\u70E7\u5F55\u6837\u7247\u4FE1\u606F\u5931\u8D25\uFF01
get.program.small.info.failed=\u83B7\u53D6\u70E7\u5F55\u5C0F\u6279\u91CF\u9A8C\u8BC1\u4FE1\u606F\u5931\u8D25\uFF01
currenthandler.is.null=\u5F53\u524D\u5904\u7406\u4EBA\u4E3A\u7A7A
exist.only.one.bracket=\u53EA\u5B58\u5728\u4E00\u4E2A\u62EC\u53F7
sn.lost.board.center={0}\u6761\u7801\u5728\u6761\u7801\u4E2D\u5FC3\u4E0D\u5B58\u5728
update.barcode.fail=\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0138\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04FF\uFFFD\u02A7\uFFFD\u0723\uFFFD{0}
error_msg_set=\u7B2C{0}\u884C\u6570\u636E\u9519\u8BEF\u4FE1\u606F:{1}
confirm_msg_set=\u7B2C{0}\u884C\u6570\u636E\u9700\u786E\u8BA4\u4FE1\u606F:\u5F53\u524D\u9009\u62E9\u7684\u914D\u9001\u5DE5\u5E8F\u4E0E\u7EF4\u62A4\u7684\u914D\u9001\u5DE5\u5E8F\u4E0D\u4E00\u81F4
no.location.data=\u65E0\u4F4D\u53F7\u6570\u636E\uFF0C\u8BF7\u5148\u8FDB\u884CBOM\u4F4D\u53F7\u89E3\u6790
supplier.coding.error=\u4F9B\u5E94\u5546\u7F16\u7801\u9519\u8BEF\uFF0C\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u7F16\u7801
item.inventory.is.null=\u7269\u6599\u63A5\u6536\u5E93\u5B58\u4E3A\u7A7A
item.not.check={0} \u7269\u6599\u672A\u8FDB\u884C\u76D8\u70B9\uFF0C\u4E0D\u5141\u8BB8\u7ED3\u675F\u76D8\u70B9
task.is.not.this.factory=\u8BE5\u4EFB\u52A1\u53F7\u7684\u751F\u4EA7\u5355\u4F4D\u4E0D\u662F\u672C\u5DE5\u5382
prod.bind.setting.is.saving=\u6599\u5355\u4EE3\u7801 {0} \u6B63\u5728\u4FDD\u5B58\u7ED1\u5B9A\u8BBE\u7F6E\u3002\u8BF7\u7A0D\u5019\u91CD\u8BD5
item.code.has.bind.on.other.process=\u7269\u6599\u4EE3\u7801 {0} \u5DF2\u7ED1\u5B9A\u5230\u5176\u5B83\u5DE5\u5E8F\u3002\u8BF7\u68C0\u67E5
get.last.package.process.fail=\u83B7\u53D6\u88C5\u914D\u6700\u540E\u5B50\u5DE5\u5E8F\u5931\u8D25
sys.look.not.config=\u6570\u636E\u5B57\u5178 {0} \u672A\u914D\u7F6E
faied.check.outsourcing.production.unit=\u67E5\u8BE2\u5916\u534F\u751F\u4EA7\u5355\u4F4D\u5931\u8D25
you.have.bill.to.approve=\u60A8\u6709\u5F85\u5BA1\u6279\u7269\u6599\u5355,\u8BF7\u53CA\u65F6\u5230iMES\u7CFB\u7EDF\u5904\u7406\u3002\u5355\u636E\u53F7\u3010{0}\u3011
prodPlanId.is.production=\u8BE5\u4EFB\u52A1\u5DF2\u6DFB\u52A0\u7F13\u51B2\u6C60\uFF0C\u4E0D\u5141\u8BB8\u53D6\u6D88\u53D1\u653E\uFF0C\u6279\u6B21\uFF1A{0}
you.have.timer.synchronize.fail=\u60A8\u6709informatica\u5B9A\u65F6\u5668\u6570\u636E\u540C\u6B65\u5931\u8D25,\u8BF7\u53CA\u65F6\u5904\u7406!
timer.synchronize.fail=\u5B9A\u65F6\u5668 {0} \u540C\u6B65\u5931\u8D25
unbinding.setting.exist=\u7ED1\u5B9A\u5F02\u5E38\u5173\u7CFB\u5DF2\u5B58\u5728\uFF08\u5305\u542B\u5B50\u5361\uFF09\uFF0C\u65E0\u9700\u91CD\u590D\u8BBE\u7F6E
productcode.is.dealing=\u4EA7\u54C1\u4EE3\u7801 {0} \u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u8BF7\u7A0D\u540E
chinese.comma.existed=\u5B58\u5728\u4E2D\u6587\u9017\u53F7\uFF0C\u8BF7\u786E\u8BA4
cad.point.lost.error={0} \u4F4D\u53F7\u4E0D\u5728CAD\u6587\u4EF6\u4E2D\u3002 </br> {1} \u7269\u6599\u5C06\u4F1A\u7EDF\u4E00\u5728A\u9762\u4EA7\u751F\u8C03\u62E8\u5355\u3002 </br>
cad.point.lost.last.infor=\u6700\u540E\u66F4\u65B0\u65F6\u95F4:{0}\uFF0C\u6700\u540E\u66F4\u65B0\u4EBA:{1}\u3002 </br>
cad.point.lost.error.sure=\u8BF7\u786E\u8BA4\u662F\u5426\u7EE7\u7EEDCAD\u89E3\u6790!
cad.point.empty=CAD \u6587\u4EF6\u4E2D\uFF0C\u53EF\u7528\u4F4D\u53F7\u4FE1\u606F\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\u6587\u4EF6\u662F\u5426\u6B63\u786E\uFF01\uFF01\uFF01
type_code_name_trace_code_name_can_not_be_empty_alone=\u524D\u52A0\u5DE5\u7C7B\u578B,\u524D\u52A0\u5DE5\u53BB\u5411\u6570\u636E\u6709\u95EE\u9898,\u4EE3\u7801,\u540D\u79F0\u4E0D\u80FD\u5355\u72EC\u4E3A\u7A7A,\u8BF7\u786E\u8BA4!
resource.match.failed=\u8D44\u6E90\u53F7\u6BB5\u683C\u5F0F\u4E0E\u8D44\u6E90\u7C7B\u578B\u4E0D\u5339\u914D\uFF01
resource.type.should.not.be.null=\u8F93\u5165\u8D44\u6E90\u53F7\u8FDB\u884C\u67E5\u8BE2\u65F6\u8D44\u6E90\u7C7B\u578B\u5FC5\u586B\uFF01
resource.type.not.exists=\u8D44\u6E90\u7C7B\u578B\u4E0D\u5B58\u5728\uFF01
resource.no.str.time.should.not.be.null=\u67E5\u8BE2\u65F6\u8D44\u6E90\u53F7\u3001\u8D44\u6E90\u7F16\u53F7\u6216\u521B\u5EFA\u65F6\u95F4\u5FC5\u9009\u4E00\u4E2A\uFF0C\u8BF7\u9009\u62E9\uFF01
search.time.limit=\u4EC5\u7528\u521B\u5EFA\u65F6\u95F4\u505A\u67E5\u8BE2\u6761\u4EF6\uFF0C\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E24\u5E74
resource.type.can.not.be.null=\u5BFC\u51FA\u65F6\u8D44\u6E90\u7C7B\u578B\u5FC5\u9009
export.time.interval.more.than.180.days=\u5BFC\u51FA\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC7\u534A\u5E74
export.time.can.not.null=\u5BFC\u51FA\u65F6\u95F4\u5FC5\u586B
export.time.interval.more.than.90.days=\u5BFC\u51FA\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC73\u4E2A\u6708
export.resourceNo.can.not.empty=\u5BFC\u51FA\u8BE6\u7EC6\u4FE1\u606F\u7684\u8D44\u6E90\u53F7\u4E0D\u80FD\u4E3A\u7A7A
export.total.more.than.100000=\u5BFC\u51FA\u6570\u91CF\u8D85\u8FC710w\uFF0C\u8BF7\u7F29\u5C0F\u9009\u62E9\u65F6\u95F4\u8303\u56F4
orderOrTask.has.already.applied.for.resources=\u8BE5\u4EFB\u52A1\u53F7\u5DF2\u7ECF\u7533\u8BF7\u8FC7\u8D44\u6E90
start.segment.must.less.end.segment=\u8D44\u6E90\u5F00\u59CB\u53F7\u6BB5\u5FC5\u987B\u5C0F\u4E8E\u7ED3\u675F\u53F7\u6BB5
duplicate.resource.number.segment=\u8D44\u6E90\u53F7\u6BB5\u5B58\u5728\u91CD\u590D\uFF0C\u8BF7\u786E\u8BA4
export.timeout=\u5BFC\u51FA\u8D85\u65F6
resource.data.error=\u8D44\u6E90\u53F7\u4E0D\u4E00\u81F4
resource.no.does.not.match=\u4EE5\u4E0B\u8D44\u6E90\u53F7\u4E0D\u5339\u914D:{0}
import.data.error=\u8D44\u6E90\u5BFC\u5165\u6570\u636E\u9519\u8BEF
import.timeout=\u5BFC\u5165\u8D85\u65F6
import.success=\u5BFC\u5165\u6210\u529F
file.format.error=\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF
resource.file.format.error={0}\u6807\u7B7E\u5305\u542B{1}\u4E2A\u53C2\u6570\uFF0C\u5BFC\u5165\u7684\u6587\u4EF6\u5305\u542B{2}\u5217\uFF0C\u6570\u91CF\u4E0D\u5339\u914D\u7CFB\u7EDF\u65E0\u6CD5\u5904\u7406\uFF0C\u8BF7\u786E\u8BA4
resource.file.header.tagparam.error=\u5BFC\u5165\u6587\u4EF6\u5934{0}\u4E0E\u6807\u7B7E\u7684{1}\u53C2\u6570\u4E0D\u5339\u914D\uFF0C\u8BF7\u786E\u8BA4
itemCode.selected.cannot.more.than.100=\u4E00\u6B21\u9009\u62E9\u7684\u6599\u5355\u4EE3\u7801\u4E0D\u8981\u8D85\u8FC7100
unknown.error.handler=\u7CFB\u7EDF\u51FA\u73B0\u672A\u77E5\u9519\u8BEF\uFF0C\u9519\u8BEF\u65E5\u5FD7\u7F16\u53F7\u4E3A\uFF1A{0},\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u4EBA\u5458\u5904\u7406\uFF0C\u8C22\u8C22\uFF01
log.id=\u65E5\u5FD7ID:{0}
task_source_cannot_be_empty=\u4EFB\u52A1\u6765\u6E90\u4E0D\u80FD\u4E3A\u7A7A
get.task.info.error=\u83B7\u53D6\u4EFB\u52A1\u4FE1\u606F\u5931\u8D25:{0}
task_has_been_issued_to_the_local_factory={0}\u4EFB\u52A1\u5DF2\u53D1\u653E\u81F3\u672C\u5730\u5DE5\u5382{1}\uFF0C\u8BF7\u786E\u8BA4
factory_not_configured_in_1245_data_dictionary=\u9009\u62E9\u5DE5\u5382\u57281245\u6570\u636E\u5B57\u5178\u672A\u914D\u7F6E\u76F8\u5173\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4\uFF01
the_batch_has_generated_a_nesting_list={0}\u6279\u6B21\u5DF2\u4EA7\u751F\u5957\u6599\u5355\uFF0C\u4E0D\u5141\u8BB8\u53D6\u6D88\u53D1\u653E\uFF01
orgid_is_null_or_factoryid_not_find=\u7EC4\u7EC7id\u6216\u5DE5\u5382id\u4E3A\u7A7A
the_current_batch_is_issued=\u5F53\u524D\u6279\u6B21\u672A\u53D1\u653E\uFF0C\u8BF7\u786E\u8BA4\uFF01
get.spm.tld.error=\u8C03SPM\u83B7\u53D6\u5957\u6599\u5355\u4FE1\u606F\u5931\u8D25
sequence.beyond.maxvalue=\u5F53\u524D\u5E8F\u5217\u53F7\u8D85\u51FA\u6700\u5927\u8BBE\u5B9A\u503C\u8BF7\u68C0\u67E5\uFF1A{0}
sequence.no.value=\u5E8F\u5217\u53F7\u521D\u8BC6\u503C\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u624B\u52A8\u65B0\u589E\uFF01
operater_time_can_not_be_empty=\u64CD\u4F5C\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
operater_time_can_not_greater_one_year=\u64CD\u4F5C\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u5927\u4E8E\u4E24\u5E74
failed.to.get.assembly.relation.push=\u83B7\u53D6{0}\u88C5\u914D\u5173\u7CFB\u63A8\u9001\u7ED3\u679C\u5931\u8D25
failed.to.update.result={0}\u6839\u636E\u6599\u5355\u4EE5\u53CA\u7248\u672C\u66F4\u65B0\u7ED3\u679C\u8868\u4EE5\u53CA\u5199\u5386\u53F2\u5931\u8D25
no.push.record.for.the.material.code={0}\u7269\u6599\u4EE3\u7801{1}\u7248\u672C\u4E0D\u5B58\u5728\u63A8\u9001\u8BB0\u5F55\uFF0C\u8BF7\u786E\u8BA4
blank.msg={0}
item.version.null=\u7269\u6599\u4EE3\u7801\u7248\u672C\u4E3A\u7A7A
update.successed=\u66F4\u65B0\u6210\u529F
item.code.length.not.twelve=\u7269\u6599\u4EE3\u7801\u4E0D\u662F12\u4F4D
taskQty.is.error=\u4EFB\u52A1\u6570\u91CF\u9519\u8BEF
selected.factory.not.the.factory.corresponding.of.task=\u9009\u62E9\u5DE5\u5382\u4E0D\u662F\u5176\u4EFB\u52A1\u7EC4\u7EC7id\u5BF9\u5E94\u7684\u53EF\u53D1\u653E\u5DE5\u5382\uFF0C\u8BF7\u786E\u8BA4
barcode.get.template.error=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6A21\u677F\u5217\u8868\u5931\u8D25
barcode.get.template.error.msg=\u8C03\u7528\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6A21\u677F\u5217\u8868\u5931\u8D25:{0}
get.lookup.value.error=\u83B7\u53D6\u6570\u636E\u5B57\u5178\u503C\u5F02\u5E38! {0}
failed_to_get_barcode_center_url=\u83B7\u53D6\u6570\u636E\u5B57\u5178\u914D\u7F6E\u6761\u7801\u4E2D\u5FC3\u63A5\u53E3URL\u5931\u8D25
failed.to.get.barcode.center.download.url=\u83B7\u53D6\u6761\u7801\u4E2D\u5FC3\u9002\u914D\u7A0B\u5E8F\u4E0B\u8F7D\u5730\u5740\u5931\u8D25
failed_to_adjust_barcode_center_print_interface=\u8C03\u6761\u7801\u4E2D\u5FC3\u6253\u5370\u63A5\u53E3\u5931\u8D25
call.barCode.center.to.print.falied=\u8C03\u6761\u7801\u4E2D\u5FC3\u6253\u5370\u6761\u7801\u5931\u8D25:{0}
dozen.sn.not.exist.print.data=\u8865\u6253\u6761\u7801{0}\u4E0D\u5B58\u5728\u5386\u53F2\u6253\u5370\u6570\u636E,\u672A\u5728IMES\u4E0A\u6253\u5370\u8FC7\u7684\u8BB0\u5F55\u8BF7\u9009\u62E9\u6253\u5370\u6A21\u7248
bar.code.center.is.null=\u6761\u7801\u4E2D\u5FC3\u4E3A\u627E\u5230\u8BE5\u8BB0\u5F55\uFF0C\u8BF7\u68C0\u67E5\u8BE5\u6761\u7801\u662F\u5426\u4E3A\u672A\u5728IMES\u4E0A\u6253\u5370\u7684\u6709\u6548\u6570\u636E:{0}
ip.is.null=ip\u4E0D\u80FD\u4E3A\u7A7A
item.no.not.exist=\u672A\u67E5\u5230\u6B64\u7269\u6599\u4EE3\u7801\u4FE1\u606F,\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u7269\u6599\u4EE3\u7801
print.count.is.null=\u6253\u5370\u4EFD\u6570\u4E0D\u80FD\u4E3A\u7A7A
print.count.can.not.more.ten=\u6253\u5370\u4EFD\u6570\u4E0D\u80FD\u5927\u4E8E10
print.num.is.null=\u6253\u5370\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
print.num.can.not.more.one.thousand=\u6253\u5370\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E1000
lead.flag.is.null=\u73AF\u4FDD\u5C5E\u6027\u4E0D\u80FD\u4E3A\u7A7A
print.template.is.null=\u6253\u5370\u6A21\u677F\u4E0D\u80FD\u4E3A\u7A7A
get.printer.failed=\u83B7\u53D6\u6253\u5370\u673A\u540D\u79F0\u5931\u8D25
failed.to.obtain.hr.info=\u8C03\u4EBA\u4E8B\u63A5\u53E3\u83B7\u53D6\u4FE1\u606F\u5931\u8D25:{0}
taskno.prodplan.is.null=\u4EFB\u52A1\u53F7\u6279\u6B21\u53F7\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
no_box_code_data_obtained=\u672A\u83B7\u53D6\u5230\u7BB1\u7801\u6570\u636E
item.type.diff=\u7269\u6599\u7C7B\u578B\u548C\u754C\u9762\u6240\u9009\u7269\u6599\u7C7B\u578B\u4E0D\u4E00\u81F4\u8BF7\u786E\u8BA4\uFF01
item.not.exists.bom=\u7269\u6599\u4EE3\u7801/\u6599\u5355\u4EE3\u7801\u5728BOM\u4E2D\u4E0D\u5B58\u5728\uFF01
item.influence.bom.msg1=\u672C\u6B21\u64CD\u4F5C\u5C06\u5F71\u54CD{0}\u4E2A\u6599\u5355\u7684\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u7CFB\u7EDF\u4E0D\u5BF9\u5386\u53F2\u7ED1\u5B9A\u5173\u7CFB\u8FDB\u884C\u5904\u7406\uFF0C\u8BF7\u786E\u8BA4\u662F\u5426\u9700\u8981\u63D0\u4EA4\uFF01
item.influence.bom.msg2=\u672C\u6B21\u64CD\u4F5C\u5C06\u5F71\u54CD{0}\u4E2A\u6599\u5355\u7684\u7ED1\u5B9A\u5173\u7CFB\uFF0C\u540E\u7EED\u65B0\u589E\u6599\u5355\u5C06\u81EA\u52A8\u589E\u52A0\u7ED1\u5B9A\u5173\u7CFB!\u5177\u4F53\u6599\u5355\u4FE1\u606F\uFF1A{1}
item.influence.bom.msg3=\u4E0D\u542B{0}\u5B50\u5DE5\u5E8F\u7684\u6599\u5355{1}\u4E2A(\u53EA\u80FD\u6599\u5355\u7EA7\u624B\u52A8\u7EF4\u62A4\u6216\u65B0\u589E\u5DE5\u827A\u8DEF\u5F84\u518D\u7EF4\u62A4),\u5177\u4F53\u6599\u5355\u4FE1\u606F\uFF1A{2}
item.has.exists=\u7269\u6599\u4EE3\u7801\u5DF2\u7ECF\u5B58\u5728\u6709\u6548\u7EF4\u62A4\u8BB0\u5F55\uFF0C\u4E0D\u80FD\u518D\u7EF4\u62A4\u8BF7\u786E\u8BA4\uFF01
route.detail.empty=\u5DE5\u827A\u8DEF\u5F84\u4E3A\u7A7A{0}\u3002
uac_token_cannot_be_empty=UAC TOKEN\u4E0D\u80FD\u4E3A\u7A7A,\u8BF7\u786E\u8BA4\uFF01
failed_to_tune_icenter_interface=\u8C03iCenter\u63A5\u53E3\u5931\u8D25,URL\uFF1A{0}
failed_to_call_icenter_interface=\u8C03iCenter\u63A5\u53E3\u5931\u8D25,MSG\uFF1A{0}
failed_to_call_icenter_to_withdraw_documents=\u8C03iCenter\u64A4\u56DE\u5355\u636E\u5931\u8D25,MSG\uFF1A{0}
the_barcode_length_is_less_than_7_digits=\u6761\u7801{0}\u957F\u5EA6\u4E0D\u8DB37\u4F4D
failed_to_get_barcode_factory_id=\u83B7\u53D6\u6761\u7801{0}\u5BF9\u5E94\u7684\u5DE5\u5382id\u5931\u8D25
failed_to_write_test_record=\u8C03\u5206\u5DE5\u5382\u5199\u6D4B\u8BD5\u8BB0\u5F55\u5931\u8D25:{0}
speciality.resource.not.exist={0}\u8D44\u6E90\u4E0D\u5B58\u5728
speciality.param.template.exist=\u6A21\u677F\u540D\u79F0\u5DF2\u5B58\u5728
speciality.param.template.not.exist={0}\u6A21\u677F\u4E0D\u5B58\u5728
speciality.param.template.item.null=\u53C2\u6570\u5217\u8868\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A
speciality.param.template.item.name.repeat=\u6A21\u677F\u53C2\u6570\u540D\u79F0\u91CD\u590D
speciality.param.template.item.brackets=\u6A21\u677F\u62EC\u53F7\u4E0D\u6210\u5BF9\uFF1A{0}
speciality.param.template.item.two.operator=\u6A21\u677F\u89C4\u5219\u64CD\u4F5C\u7B26\u9519\u8BEF\uFF1A{0}
speciality.param.template.item.function=\u6A21\u677F\u89C4\u5219\u51FD\u6570\u9519\u8BEF\uFF1A{0}
speciality.param.template.item.rule.err={0}\u89C4\u5219\u9519\u8BEF,\u8BF7\u786E\u8BA4\u540E\u91CD\u8BD5!
speciality.param.template.item.error=\u6A21\u677F{0}\u89C4\u5219\u4E2D\u53C2\u6570\u4E0D\u5B58\u5728\u6216\u6392\u5E8F\u9519\u8BEF
speciality.param.applyTask.generate={0}\u8BA2\u5355\u53F7\u5DF2\u751F\u6210\u8FC7\u4E2A\u53C2\uFF0C\u8BF7\u786E\u8BA4
speciality.param.applyTask.not.resource={0}\u8BA2\u5355\u65E0{1}\u8D44\u6E90\u7533\u8BF7\u5355\uFF0C\u65E0\u6CD5\u751F\u6210\u4E2A\u53C2\uFF0C\u8BF7\u786E\u8BA4
speciality.param.applyTask.resource.not.match={0}\u8D44\u6E90\u7533\u8BF7\u5355{1}\u4E0E{2}\u8D44\u6E90\u7533\u8BF7\u5355{3}\u7684\u7533\u8BF7\u6570\u91CF\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u751F\u6210\u4E2A\u53C2\uFF0C\u8BF7\u786E\u8BA4
excel.read.failed=\u6587\u4EF6\u89E3\u6790\u5931\u8D25
table.column.error=\u8868\u683C\u5217\u6709\u8BEF
status.of.taskNo.is.released=\u6807\u6A21\u4EFB\u52A1\u53F7:{0}\u7684\u4EFB\u52A1\u72B6\u6001\u4E3A\u201C\u5DF2\u53D1\u653E\u201D
length.of.data.exceeds.500=\u5BFC\u5165\u6570\u636E\u4E0D\u80FD\u8D85\u8FC7500\u6761
taskNo.or.prodplanId.fields.cannot.be.empty=\u6807\u6A21\u4EFB\u52A1\u548C\u6279\u6B21\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A
fields.such.as.assembly.remarks.must.not.be.empty=\u6807\u6A21\u4EFB\u52A1{0}\u6279\u6B21{1}\u5BF9\u5E94\u7684\u88C5\u914D\u5907\u6CE8\u3001\u8BA1\u8C03\u5907\u6CE8\u3001\u8981\u6C42\u88C5\u914D\u5B8C\u5DE5\u65F6\u95F4\u3001\u8981\u6C42\u6D4B\u8BD5\u5B8C\u5DE5\u65F6\u95F4\u3001\u8981\u6C42\u5305\u88C5\u5B8C\u5DE5\u65F6\u95F4\u3001\u9884\u8BA1\u53D1\u653E\u65E5\u3001\u5DE5\u5E8F\u6307\u4EE4\u6309\u4EFB\u52A1\u6761\u7801\u6821\u9A8C\u3001\u662F\u5426\u9501\u5B9A\u4EFB\u52A1\u6761\u7801\u5165\u5E93\u3001\u662F\u5426\u89E6\u53D1\u62C9\u6599\u9700\u6709\u4EFB\u4E00\u5B57\u6BB5\u4E0D\u4E3A\u7A7A
duplicate.data.in.the.table=\u6807\u6A21\u4EFB\u52A1\u53F7:{0}\u5728\u8868\u5185\u5B58\u5728\u91CD\u590D\u6570\u636E
taskNo.and.prodplanId.do.not.match=\u6807\u6A21\u4EFB\u52A1\u53F7:{0}\u548C\u6279\u6B21:{1}\u4E0D\u5339\u914D
failed_to_process_the_document_in_the_factory=\u8C03\u5206\u5DE5\u5382\u5904\u7406\u5355\u636E\u5931\u8D25:{0}
failed_to_process_approval_center_kafka_message=\u5904\u7406\u5BA1\u6279\u4E2D\u5FC3kafka\u6D88\u606F\u5931\u8D25
task.no.not.exists=\u4EFB\u52A1:{0}\u4E0D\u5B58\u5728
type.name.should.not.be.null=\u524D\u52A0\u5DE5\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
process.code.id.exists=\u5B50\u5DE5\u5E8F\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
process.name.exists=\u5B50\u5DE5\u5E8F\u540D\u79F0\u5DF2\u5B58\u5728\uFF01
station.is.existed=\u5DE5\u7AD9\u5DF2\u5B58\u5728!
insert.workstaion.is.success=\u6DFB\u52A0\u5DE5\u7AD9\u6210\u529F!
single.page.query.cannot.exceed.500.entries=\u5355\u9875\u67E5\u8BE2\u4E0D\u5F97\u8D85\u8FC7500\u6761
params.is.null=\u53C2\u6570\u4E3A\u7A7A
query.noCtBasic.by.craftId=\u5F53\u524D\u5DE5\u827A\u4FE1\u606F\u5DF2\u5347\u7EA7\uFF0C\u8BF7\u5237\u65B0
existing.uncommit.craft={0} \u5DF2\u5B58\u5728\u62DF\u5236\u4E2D\u5DE5\u827A
save.sucess=\u4FDD\u5B58\u6210\u529F
not.find.craft.info=\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5DE5\u827A\u57FA\u7840\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5
must.be.board.assembly=\u5355\u677F\u88C5\u914D\u7684\u7B2C\u4E00\u4E2A\u5DE5\u5E8F\u5FC5\u987B\u662F\u5355\u677F\u88C5\u914D\u6295\u677F
must.be.board.test=\u5355\u677F\u6D4B\u8BD5\u7684\u7B2C\u4E00\u4E2A\u5DE5\u5E8F\u5FC5\u987B\u662F\u5355\u677F\u6D4B\u8BD5\u6295\u677F
this.version.craft.exists=\u8BE5\u7248\u672C\u5DF2\u5B58\u5728 {0}-{1} \u5DE5\u827A\u8DEF\u5F84
the_current_version_is_malformed=\u5F53\u524D\u7248\u672C\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4
the_version_number_is_temporarily_only_supported=\u7248\u672C\u53F7\u6682\u65F6\u53EA\u652F\u6301V.+\u4E09\u4F4D,\u8BF7\u786E\u8BA4!
not.find.route.head.info=\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5DE5\u827A\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5
not.find.route.detail.info=\u672A\u627E\u5230\u5BF9\u5E94\u7684\u5DE5\u827A\u8BE6\u60C5\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5
craft_version_exist_submited=\u8BE5\u6599\u5355\u5DF2\u5B58\u5728\u7248\u672C{0}\u7684\u5DF2\u63D0\u4EA4\u7684\u5DE5\u827A\u6570\u636E
craft_version_exist=\u8BE5\u6599\u5355\u5DF2\u5B58\u5728\u7248\u672C{0}\u7684\u5DE5\u827A\u6570\u636E
tags.param.is.null=\u6807\u7B7E\u53C2\u6570\u5217\u8868\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
task.no.pulling=\u5F53\u524D\u4EFB\u52A1{0}\u6B63\u5728\u4ECEaps \u62C9\u53D6\u6570\u636E\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002
task.no.not.exist.aps=\u5F53\u524D\u4EFB\u52A1\u53F7{0}\u5728IAPS \u6CA1\u6709\u6709\u6548\u6570\u636E\u8BF7\u786E\u8BA4\u3002
task.no.exist.imes=\u5F53\u524D\u4EFB\u52A1\u53F7{0}\u5728IMES\u5B58\u5728,\u4E0D\u80FD\u518D\u62C9\u53D6\u3002
prod.address.empty=\u914D\u9001\u5730{0}\u914D\u7F6E\u9879\u7F3A\u5931\uFF0C\u8BF7\u68C0\u67E5\u3002
bom.id.miss=BOM \u6570\u636E\u7F3A\u5931 bomId:{0},\u4EFB\u52A1\u53F7\uFF1A{1}
sn_carton_relation_err=infor\u53D1\u6599\u6570\u91CF\u4E3A{0}, \u5355\u636E\u53F7:{1}, \u7BB1\u53F7:{2}, SN:{3}, \u4E0E\u5B9E\u9645\u53D1\u6599\u6570\u91CF\u4E0D\u7B26\uFF0C\u8BF7\u68C0\u67E5\uFF01
board.assembly.query.params.not.null=\u4E3B\u6761\u7801\uFF0C\u5B50\u6761\u7801\uFF0C\u5F00\u59CB/\u7ED3\u675F\u65F6\u95F4\u5FC5\u987B\u8F93\u5165\u4E00\u4E2A
board.assembly.query.time.not.paired=\u5F00\u59CB\u65F6\u95F4\u548C\u7ED3\u675F\u65F6\u95F4\u5FC5\u987B\u540C\u65F6\u8F93\u5165
board.assembly.query.params.not.one.condition=\u4E3B\u6761\u7801\uFF0C\u5B50\u6761\u7801\uFF0C\u5F00\u59CB/\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u7EC4\u5408\u8F93\u5165
board.assembly.query.page.and.row.not.null=\u4F7F\u7528\u65F6\u95F4\u533A\u95F4\u67E5\u8BE2\u65F6\u9700\u8981\u8F93\u5165\u5206\u9875\u53C2\u6570
board.assembly.query.time.interval.exceeds.one.month=\u4F7F\u7528\u65F6\u95F4\u533A\u95F4\u67E5\u8BE2\u65F6\u65F6\u95F4\u533A\u95F4\u4E0D\u80FD\u8D85\u8FC731\u5929
board.assembly.query.row.too.large=\u4F7F\u7528\u65F6\u95F4\u533A\u95F4\u67E5\u8BE2\u65F6\uFF0C\u5206\u9875\u7684\u6BCF\u4E00\u9875\u884C\u6570\u4E0D\u80FD\u8D85\u51FA100
recovery.finished=\u8D44\u6E90\u5DF2\u56DE\u6536\uFF0C\u8BF7\u68C0\u67E5
prod.plan.sequence.over= \u4E2D\u5FC3\u5DE5\u5382\u6279\u6B21\u5E8F\u5217\u8FBE\u5230\u9884\u8B66\u503C\uFF0C\u8BF7\u53CA\u65F6\u5904\u7406\uFF0C\u5F53\u524D\u5E8F\u5217\uFF1A{0}\uFF0C\u6700\u5927\u5E8F\u5217\uFF1A{1},\u9608\u503C:{2}
prod.plan.exist.center.factory= {0} \u6279\u6B21\u518D\u4E2D\u5FC3\u5DE5\u5382\u5B58\u5728\uFF0C\u4E0D\u80FD\u4F7F\u7528\u3002
kafka.msg.save.database=kafka \u6279\u6B21\u6D88\u606F\u6D88\u8D39\u5931\u8D25\uFF0C\u5DF2\u7ECF\u5B58\u5165\u672C\u5730\u6D88\u606F\u8868\uFF0C\u8BF7\u53CA\u65F6\u5206\u6790\u5904\u7406,\u6D88\u606Fid:{0}
no.resource.recovery=\u6CA1\u6709\u8D44\u6E90\u8981\u56DE\u6536\uFF0C\u8BF7\u68C0\u67E5
excel.content.format.not.match=Excel\u5185\u5BB9\u548C\u683C\u5F0F\u4E0D\u5339\u914D
the_current_status_of_the_approver_is_finished=\u672A\u627E\u5230\u8BE5\u8282\u70B9\u8BE5\u5BA1\u6279\u4EBA\u5BF9\u5E94\u7684\u672A\u5BA1\u6279\u4FE1\u606F\uFF0C\u53EF\u80FD\u5DF2\u5B8C\u6210\u5BA1\u6279\uFF0C\u8BF7\u786E\u8BA4
current_document_information_not_found=\u672A\u627E\u5230\u5F53\u524D\u5355\u636E\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4!
params.err=\u8F93\u5165\u53C2\u6570\u4E0D\u6B63\u786E
mainCraftSection.is.null=\u8BF7\u9009\u62E9\u6267\u884C\u4E3B\u5DE5\u5E8F
a.maximum.of.ten.data.can.be.queried=\u6700\u591A\u67E5\u8BE2\u5341\u6761\u6570\u636E
check_that_the_product_code_is_empty=\u8BF7\u68C0\u67E5\u4EA7\u54C1\u4EE3\u7801\u6216\u8005\u4F4D\u53F7\u662F\u5426\u4E3A\u7A7A
aps.proplanId.msg.error=APS \u6279\u6B21\u540C\u6B65\u6D88\u606F\u6570\u636E\u5F02\u5E38\u8BF7\u68C0\u67E5\uFF01
factory.id.of.center.pstask.is.null=IMES\u4E2D\u5FC3\u5DE5\u5382\u4E2D\u8BE5\u4EFB\u52A1\u53F7\u6240\u5C5E\u5DE5\u5382ID\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
factory.id.of.center.pstask.is.illegality=IMES\u4E2D\u5FC3\u5DE5\u5382\u4E2D\u8BE5\u4EFB\u52A1\u53F7\u6240\u5C5E\u5DE5\u5382ID\u4E0D\u5408\u6CD5\uFF0C\u8BF7\u786E\u8BA4\uFF01
center.factory.not.exist.the.task.no=IME\u4E2D\u5FC3\u5DE5\u5382\u4E0D\u5B58\u5728\u8BE5\u4EFB\u52A1\u53F7,\u8BF7\u786E\u8BA4\uFF01
prodplanno.not.null=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A
prodplanmodifyno.not.null=\u53D8\u66F4\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
failed_to_deal_technical_bill=\u5F53\u524D\u6280\u6539\u5355{0}\u5904\u7406\u5931\u8D25,\u8BF7\u786E\u8BA4\uFF01
more_than_50_batches=\u6280\u6539\u5355{0}\u5728\u7EBF\u6279\u6B21\u8D85\u8FC750\u4E2A,\u8BF7\u91CD\u65B0\u63D0\u5355\uFF01
reelid_already_exists=reelid:{0}\u5DF2\u5B58\u5728
kafka.msg.save.database.task.update=\u8BA1\u5212\u4EFB\u52A1\u4FEE\u6539kafka\u6D88\u606F \u6D88\u8D39\u5931\u8D25\uFF0C\u5DF2\u7ECF\u5B58\u5165\u672C\u5730\u6D88\u606F\u8868\uFF0C\u8BF7\u53CA\u65F6\u5206\u6790\u5904\u7406,\u6D88\u606Fid:{0}
get.boardonline.info.failed = \u83B7\u53D6board_online\u4FE1\u606F\u5931\u8D25
get.barsubmit.info.failed = \u83B7\u53D6barsubmit\u4FE1\u606F\u5931\u8D25
failed.to.update.sys.look.up.meaning=\u66F4\u65B0\u6570\u636E\u5B57\u5178\u503C\u5931\u8D25
query.params.lost=\u4EFB\u52A1\u53F7\u3001\u6279\u6B21\u53F7\u3001\u6599\u5355\u4EE3\u7801\u3001\u6761\u7801\u5FC5\u4F20\u4E00\u4E2A

technical_info_lost=\u6280\u6539\u4FE1\u606F\u540C\u6B65\u5931\u8D25,\u8BF7\u786E\u8BA4\uFF01
technical_info_outnumber=\u6280\u6539\u56DE\u5199\u6761\u7801\u6570\u91CF\u8D85\u8FC7500\u4E07,\u4F20\u7ED9MES\u5B58\u5728\u4E00\u5B9A\u5EF6\u8FDF\uFF0C\u8BF7\u5173\u6CE8\uFF01
aps.task.status.error={0} \u5728APS\u4EFB\u52A1\u72B6\u6001\u4E0D\u662F\u5DF2\u53D1\u653E\uFF0C\u4E0D\u80FD\u624B\u52A8\u62C9\uFF0C\u8BF7\u786E\u8BA4\uFF01
the_process_path_of_the_material_list_is_not_found=\u672A\u627E\u5230\u6599\u5355\u7684\u5DE5\u827A\u8DEF\u5F84\u6570\u636E,\u7CFB\u7EDF\u672A\u81EA\u52A8\u89E3\u6790\u7ED1\u5B9A\u5173\u7CFB
product.code.include.pcb={0}\u6599\u5355\u5305\u542BPCB\u8BF7\u70B9\u51FB\u624B\u5DE5\u6267\u884CBOM\u5206\u9636\u6309\u94AE\u8FDB\u884C\u8BA1\u7B97!
the.material.list.code.should.contain.15.characters=\u6599\u5355\u4EE3\u7801\u957F\u5EA6\u5E94\u4E3A15\u4E2A\u5B57\u7B26\uFF0C\u8BF7\u68C0\u67E5\uFF01
no.bom.information.is.found=\u672A\u627E\u5230\u6599\u5355\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5\uFF01
synchronize.spm.data.warning=\u540C\u6B65SPM\u6280\u6539\u4FE1\u606F\u90E8\u5206\u6210\u529F\uFF0C\u5B58\u5728\u4EE5\u4E0B\u6570\u636E\u9700\u8981\u5206\u6790\u5904\u7406\uFF1A{0}
synchronize.spm.data.error=\u540C\u6B65SPM\u6280\u6539\u4FE1\u606F\u5931\u8D25\uFF0C\u540C\u6B65\u65F6\u95F4\u533A\u95F4\u4E3A{0}\u81F3{1}\uFF0C\u5931\u8D25\u539F\u56E0\uFF1A
get.last.sync.time.error=\u83B7\u53D6SPM\u6700\u540E\u540C\u6B65\u65F6\u95F4\u5931\u8D25\uFF0C\u5177\u4F53\u9519\u8BEF\u4FE1\u606F\u5982\u4E0B\uFF1A
spm.data.not.have.chg.reg.no=SPM\u8BE5\u884C\u6570\u636E\u65E0\u6280\u6539\u5355\u53F7
spm.data.not.have.prod.id=SPM\u8BE5\u884C\u6570\u636E\u65E0\u6279\u6B21\u53F7
the_itemnos_is_more_than_three_hundred=\u6599\u5355\u4EE3\u7801\u6570\u91CF\u8D85\u8FC7\u4E09\u767E\u9650\u5236\uFF01
the_itemnos_is_more_than_one_thousand=\u6599\u5355\u4EE3\u7801\u6570\u91CF\u8D85\u8FC7\u4E00\u5343\u9650\u5236\uFF01
the_itemnos_is_more_than_ten=\u8F93\u5165\u7684\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u8D85\u8FC710\u4E2A\uFF01
params.can.not.be.null=\u6599\u5355\u4EE3\u7801\u548C\u6599\u5355\u540D\u79F0\u5FC5\u8F93\u5176\u4E00
query.bom.no.null=\u672A\u83B7\u53D6\u5230\u6599\u5355\u6570\u636E
param.size.between.1.and.1000=\u53C2\u6570\u4E2A\u6570\u5FC5\u987B\u4E3A1-1000
task.no.cannot.be.null=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
send.semi.wip.ext.by.spm.lock=\u6B63\u5728\u56DE\u5199\u534A\u6210\u54C1\u88C5\u914D\u5173\u7CFB\u4E2D
send.material.wip.ext.by.spm.lock=\u6B63\u5728\u56DE\u5199\u539F\u6750\u6599\u88C5\u914D\u5173\u7CFB\u4E2D
no.find.wip.ext.prod.plan=\u672A\u627E\u5230\u6279\u6B21{0}\uFF0C\u4EE5\u4E0A\u6279\u6B21\u6761\u7801\u88C5\u914D\u5173\u7CFB\u56DE\u5199imes\u5931\u8D25\uFF0C\u8BF7\u5173\u6CE8\uFF01
push.wip.ext.to.factory.failed=\u88C5\u914D\u5173\u7CFB\u56DE\u5199\u672C\u5730\u5DE5\u5382\u5931\u8D25{0}
failed_to_obtain_prod_plan_imes_batch_information=\u83B7\u53D6prod_plan_imes\u6279\u6B21\u4FE1\u606F\u5931\u8D25
param.missing=\u5FC5\u586B\u53C2\u6570\u7F3A\u5931
query.params.empty.except.create.by=\u521B\u5EFA\u4EBA\u4EE5\u5916\u7684\u67E5\u8BE2\u65F6\u95F4\u4E0D\u80FD\u5747\u4E3A\u7A7A
is.updating.please.wait=\u5F53\u524D\u6599\u5355\u6B63\u5728\u6267\u884C\u64CD\u4F5C,\u8BF7\u7A0D\u540E
bom.temperature.is.exited={0}\u5DE5\u827A\u6BB5,{1}\u6599\u5355,{2}\u9762,{3},{4},\u5DF2\u5B58\u5728\u7089\u6E29\u540D\u79F0,\u8BF7\u786E\u8BA4!
failed_to_get_spm_lock_information=\u83B7\u53D6SPM\u9501\u5B9A\u9501\u5B9A\u4FE1\u606F\u5931\u8D25
failed_to_write_local_factory_lock_information=\u5199\u672C\u5730\u5DE5\u5382\u9501\u5B9A\u4FE1\u606F\u5931\u8D25\uFF1A{0}
production_type_already_exists=\u5DF2\u5B58\u5728\u751F\u4EA7\u7C7B\u578B {0} \u7684\u5DE5\u827A\u8DEF\u5F84\uFF0C\u4E0D\u80FD\u518D\u65B0\u589E\u751F\u4EA7\u7C7B\u578B {1} \u7684\u5DE5\u827A\u8DEF\u5F84
the_process_path_of_the_maintained_process_type=\u5DF2\u7EF4\u62A4\u5DE5\u827A\u7C7B\u578B {0} \u7684\u5DE5\u827A\u8DEF\u5F84
the_current_process_path_is_being_processed=\u5F53\u524D\u5DE5\u827A\u8DEF\u5F84\u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
currently_exporting=\u5F53\u524D\u6B63\u5728\u5BFC\u51FA
lookup.6001.empty=\u5355\u677F\u7EDF\u8BA1\u65E5\u62A5\u6570\u636E\u5B57\u6BB5\u914D\u7F6E\uFF086001\uFF09\u4E3A\u7A7A
data_volume_exceeds_5_w=\u6570\u636E\u91CF\u8D85\u8FC75W\uFF0C\u5DF2\u90AE\u4EF6\u5BFC\u51FA\uFF0C\u8BF7\u5173\u6CE8\u90AE\u4EF6\u4FE1\u606F
no.bom.information.or.no.route=\u672A\u627E\u5230\u6599\u5355\u4FE1\u606F\u6216\u672A\u7EF4\u62A4\u5DE5\u827A\u8DEF\u5F84,\u8BF7\u786E\u8BA4\uFF01
center.task.query.param.is.null=\u4EFB\u52A1\u4FE1\u606F\u67E5\u8BE2\u53C2\u6570\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
center.task.query.page.or.row.illegal=\u4EFB\u52A1\u4FE1\u606F\u67E5\u8BE2\u9875\u7801\u548C\u884C\u6570\u975E\u6CD5\uFF0C\u8BF7\u786E\u8BA4\uFF01
center.task.query.five.params.not.all.null=\u4EFB\u52A1\u4FE1\u606F\u67E5\u8BE2\u4EFB\u52A1\u53F7\u3001\u6279\u6B21\u3001\u91CA\u653E\u65E5\u3001\u9884\u8BA1\u53D1\u653E\u65E5\u3001\u53D1\u653E\u65E5\u6761\u4EF6\u4E0D\u80FD\u5168\u90E8\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
center.task.query.time.larger.2.years.when.not.prod.and.task=\u4EFB\u52A1\u53F7\u3001\u6279\u6B21\u672A\u8F93\u5165\u65F6\uFF0C\u91CA\u653E\u65E5\u3001\u9884\u8BA1\u53D1\u653E\u65E5\u3001\u53D1\u653E\u65E5\u82E5\u4E3A\u67E5\u8BE2\u6761\u4EF6\uFF0C\u65F6\u95F4\u4E0D\u80FD\u8D85\u8FC72\u5E74(365\u5929*2)
customer.items.params.null=\u53C2\u6570\u4E3A\u7A7A
customer.type.null=\u7C7B\u578B\u4E3A\u7A7A
customer.params.null.four=\u7C7B\u578B\u4E3A{0},\u5BA2\u6237\u540D\u79F0\u3001ZTE\u4EE3\u7801\u3001\u4F9B\u5E94\u5546\u3001\u89C4\u683C\u578B\u53F7\u5747\u4E0D\u80FD\u4E3A\u7A7A
customer.params.null.one.two.zero=\u7C7B\u578B\u4E3A{0},\u9879\u76EE\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
customer.params.null.other=\u7C7B\u578B\u4E3A{0},\u5BA2\u6237\u540D\u79F0\u3001\u9879\u76EE\u540D\u79F0\u3001ZTE\u4EE3\u7801\u5747\u4E0D\u80FD\u4E3A\u7A7A
customer.items.add.or.update=\u5F53\u524D\u6570\u636E\u6B63\u5728\u63D0\u4EA4,\u8BF7\u7A0D\u540E\u518D\u8BD5
check.error.please.reselect.file.parsing=\u6821\u9A8C\u51FA\u9519\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u6587\u4EF6\u89E3\u6790
customer.items.exist=\u5F53\u524D\u7269\u6599\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u4E0D\u5141\u8BB8{0}\uFF0C\u8BF7\u68C0\u67E5{1}
customer.qry.params.null=\u9879\u76EE\u540D\u79F0\u3001\u65F6\u95F4\u4E0D\u80FD\u5747\u4E3A\u7A7A
qry.time.can.not.greater.one.year=\u67E5\u8BE2\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E24\u5E74
no.id.cannot.delete=\u8BF7\u4F20\u5165id
sub.customer.null=\u8BF7\u4F20\u5165\u5B50\u516C\u53F8\u540D\u79F0
item.no.list.null=\u8BF7\u4F20\u5165\u7269\u6599\u4EE3\u7801
max.item.no.is.once=\u4E00\u6B21\u6700\u591A\u53EA\u80FD\u67E5\u8BE2500\u4E2A\u7269\u6599\u4EE3\u7801\u4FE1\u606F
sub.customer.not.exist=\u6570\u636E\u5B57\u51787300\u672A\u914D\u7F6E{0}\u5B50\u516C\u53F8\u4FE1\u606F
size.of.data.too.large=\u63A8\u9001\u7684\u6570\u636E\u6BCF\u6B21\u4E0D\u80FD\u8D85\u8FC7500\u6761\uFF0C\u8BF7\u786E\u8BA4\uFF01
push.time.within.180.days=\u63A8\u9001\u65F6\u95F4\u8303\u56F4\u8D85\u8FC7180\u5929
log.data.query.param.null=\u5BA2\u6237\u6570\u636E\u63A8\u9001\u65E5\u5FD7\u67E5\u8BE2\u53C2\u6570\u4E0D\u80FD\u5168\u90E8\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
log.data.query.param.not,with.time=\u5BA2\u6237\u540D\u79F0\uFF0C\u6570\u636E\u7C7B\u522B\uFF0C\u63A8\u9001\u72B6\u6001\u9700\u8981\u548C\u63A8\u9001\u65F6\u95F4\u4E00\u8D77\u67E5\u8BE2
log.data.lookup.type.error=\u83B7\u53D6\u63A8\u9001\u63A5\u53E3\u4FE1\u606F\u5931\u8D25,\u8BF7\u786E\u8BA4\u662F\u5426\u914D\u7F6E\u6570\u636E\u5B57\u5178{0}
log.data.not.match.lookup.type=\u91CD\u63A8\u6570\u636E\u6CA1\u6709\u5339\u914D\u5230\u5BF9\u5E94\u7684\u6570\u636E\u7C7B\u578B\uFF0C\u8BF7\u786E\u8BA4\uFF01
sign.illegal=\u7B7E\u540D\u975E\u6CD5\uFF0C\u8BF7\u786E\u8BA4\uFF01
url.empty=\u8BF7\u6C42ulr\u4E0D\u80FD\u4E3A\u7A7A
zj.customer.model.null=\u7C7B\u578B\u4E3A\u6574\u673A\u65F6\u5BA2\u6237\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
workshop_capacity_already_exists={0}, \u8F66\u95F4\u8D44\u6E90\u80FD\u529B\u5DF2\u5B58\u5728
spare.part.detail.error=\u8F85\u6599\u8C03\u62E8\u660E\u7EC6\u5B58\u5728\u8F85\u6599\u540D\u79F0/\u6570\u91CF\u4E3A\u7A7A\u7684\u6570\u636E
spare.part.approve.error=\u8F85\u6599\u8C03\u62E8\u5BA1\u6279\u660E\u7EC6\u5B58\u5728\u5BA1\u6279\u4EBA/\u5C97\u4F4D\u4E3A\u7A7A\u7684\u6570\u636E
spare.part.bill.lost=\u8F85\u6599\u8C03\u62E8\u5355\u636E{0}\u4FE1\u606F\u4E0D\u5B58\u5728
spare.part.status.error=\u8F85\u6599\u8C03\u62E8\u5355\u636E\u72B6\u6001\u4E0D\u662F\u62DF\u5236\u4E2D/\u6821\u9A8C\u5931\u8D25\u4E0D\u80FD\u4FEE\u6539
spare.part.status.error.msg=\u8F85\u6599\u8C03\u62E8\u5355\u636E\u72B6\u6001\u5F85\u9A8C\u8BC1\u4E0D\u80FD\u4FEE\u6539
spare.part.partName.error=\u540D\u79F0\u4E3A\u7A7A
spare.part.quantity.error=\u6570\u91CF\u53EA\u80FD\u662F\u6B63\u6574\u6570
spare.part.partName.repetition=\u5355\u636E\u660E\u7EC6\u540D\u79F0\u91CD\u590D
spare.part.factory.error=\u63A5\u6536\u5DE5\u5382\u548C\u8C03\u62E8\u5DE5\u5382\u4E0D\u80FD\u662F\u540C\u4E00\u4E2A
spare.part.approve.lost=\u7F3A\u5931\u5BA1\u6279\u8282\u70B9\uFF0C\u8BF7\u6DFB\u52A0{0}
spare.part.bill.no.not.null=\u8C03\u62E8\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
get.fixture.model.head.id.null=\u83B7\u53D6\u5DE5\u88C5\u578B\u53F7\u5931\u8D25\uFF0CheadId\u4E3A\u7A7A
item.detail.can.not.be.empty=\u8C03\u62E8\u7269\u6599\u8BE6\u60C5\u4E0D\u80FD\u4E3A\u7A7A
not.in.the.document.details={0}\u4E0D\u5728{1}\u5355\u636E\u62E3\u6599\u660E\u7EC6\u4E2D
close.bill.detail.allocation.status.wrong=\u8C03\u62E8\u5355\u636E\u4E0B\u5BF9\u5E94\u8F85\u6599\u8C03\u62E8\u72B6\u6001\u5B58\u5728\u975E\u5DF2\u56DE\u9000\u6216\u5DF2\u8C03\u62E8\u72B6\u6001\uFF0C\u65E0\u6CD5\u5173\u95ED\uFF0C\u8BF7\u786E\u8BA4\uFF01
spare.part.allocation.query.param.null=\u67E5\u8BE2\u53C2\u6570\u9519\u8BEF\uFF0C\u8C03\u62E8\u5355\u3001\u5907\u54C1\u8F85\u6599\u7F16\u7801\u53EF\u76F4\u63A5\u67E5\u8BE2\uFF0C\u5176\u4F59\u6761\u4EF6\u9700\u914D\u5408\u63A5\u6536\u65F6\u95F4/\u8C03\u51FA\u65F6\u95F4/\u8C03\u62E8\u5355\u67E5\u8BE2
day.over.one.year=\u65F6\u95F4\u8D85\u8FC7\u4E00\u5E74\uFF0C\u4E0D\u80FD\u67E5\u8BE2
duplicate_material_storage_attributes=\u6761\u7801\u6216\u7269\u6599\u4EE3\u7801+\u4F9B\u5E94\u5546\u5DF2\u5B58\u5728\u7269\u6599\u5B58\u50A8\u5C5E\u6027\uFF01
sn.list.of.ca.certificate.null=\u5F85\u5BFC\u5165\u8BC1\u4E66\u7684\u6807\u6A21\u6761\u7801\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
sn.list.of.ca.certificate.more.200=\u672C\u6B21\u5F85\u5BFC\u5165\u8BC1\u4E66\u7684\u6807\u6A21\u6761\u7801\u6570\u91CF\u8D85\u51FA200,\u4E0D\u5141\u8BB8\u5BFC\u5165,\u8BF7\u786E\u8BA4\uFF01
sn.ca.bind.have.sn.info=\u6761\u7801\u5DF2\u5BFC\u5165\uFF0C\u65E0\u9700\u518D\u6B21\u5BFC\u5165
sn.ca.check.error=\u6761\u7801{0}\u5931\u8D25\u539F\u56E0:{1};
sn.ca.check.error.head=\u90E8\u5206\u6761\u7801\u6821\u9A8C\u672A\u901A\u8FC7\uFF0C\u5982\u4E0B\uFF1A
page.params.of.ca.query.null=\u67E5\u8BE2CA\u8BC1\u4E66\uFF0C\u5206\u9875\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
sn.list.of.ca.query.null=\u67E5\u8BE2CA\u8BC1\u4E66\uFF0C\u6574\u673A\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
barcode.not.registered=\u5B58\u5728\u672A\u5728\u6761\u7801\u4E2D\u5FC3\u6CE8\u518C\u6761\u7801{0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
date.range.repeat=\u516C\u5047\u65F6\u95F4\u4E0E\u5DF2\u6709\u516C\u5047:{0} {1}\u5B58\u5728\u91CD\u590D\uFF0C\u8BF7\u786E\u8BA4\uFF01
holiday.params.error=\u516C\u5047\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u5168\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
holiday.repeat=\u5DF2\u7EF4\u62A4\u8BE5\u5E74\u5BF9\u5E94\u516C\u5047{0} {1}\uFF0C\u65E0\u6CD5\u65B0\u589E\uFF0C\u8BF7\u4FEE\u6539\u6216\u8005\u5220\u9664\u539F\u516C\u5047
holiday.date.null=\u65B0\u589E/\u4FEE\u6539\u4F20\u5165\u65F6\u95F4\u53C2\u6570\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
holiday.year.error=\u516C\u5047\u5E74\u4EFD\u4E0E\u65F6\u95F4\u8303\u56F4\u4E2D\u5E74\u4EFD\u4E0D\u540C\uFF0C\u8BF7\u786E\u8BA4\uFF01
pws.is.wrong=\u63A5\u6536\u4EBA\u8F93\u5165\u5BC6\u7801\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\uFF01
factory.id.is.same=\u5F53\u524D\u9009\u62E9\u5DE5\u5382\u548C\u5DF2\u53D1\u653E\u5DE5\u5382\u4E00\u81F4\uFF0C\u4E0D\u80FD\u53D6\u6D88\u53D1\u653E
batch_has_a_lock_order=\u6279\u6B21\u5B58\u5728\u9501\u5B9A\u5355{0}\uFF0C\u8BF7\u89E3\u9501\u4E4B\u540E\u518D\u53D6\u6D88\u53D1\u653E
no.product.has.process.code=\u6D89\u53CA\u672A\u7ED1\u5B9A\u6599\u5355\u5DE5\u827A\u8DEF\u5F84\u5747\u4E0D\u5305\u542B{0}\u5B50\u5DE5\u5E8F\uFF0C\u8BF7\u786E\u8BA4
pls.select.process.code=\u8BF7\u9009\u62E9\u5B50\u5DE5\u5E8F
process.code.cannot.be.n=\u5B50\u5DE5\u5E8F\u4E0D\u80FD\u4E3A\u5165\u5E93
date.range.more.than.90.days=\u65F6\u95F4\u8DE8\u5EA6\u8D85\u8FC790\u5929

dimension_null=\u7EF4\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
production_unit_null=\u751F\u4EA7\u5355\u4F4D\u4E0D\u80FD\u4E3A\u7A7A
work_order_category_null=\u5DE5\u5355\u7C7B\u522B\u4E0D\u80FD\u4E3A\u7A7A
plan_group_null=\u8BA1\u5212\u7EC4\u4E0D\u80FD\u4E3A\u7A7A
model_name_null=\u673A\u578B\u4E0D\u80FD\u4E3A\u7A7A
utilization_rate_null=\u4EA7\u80FD\u5229\u7528\u7387\u4E0D\u80FD\u4E3A\u7A7A
capacity_day_null=\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
model_or_plan_group_more_then_ten=\u673A\u578B\u6216\u8BA1\u5212\u7EC4\u4E0D\u80FD\u8D85\u8FC710\u4E2A

resource.warning.waterLevel.title=\u8D44\u6E90\u6C60\u5B58\u5728\u4F4E\u4E8E\u6C34\u4F4D\u503C\u7684\u8D44\u6E90\uFF0C\u8BF7\u53CA\u65F6\u5904\u7406!
resource.warning.waterLevel.tip=\u4EE5\u4E0B\u8D44\u6E90\u5DF2\u4F4E\u4E8E\u6C34\u4F4D\u503C:
resource.warning.expiryDate.title=\u8D44\u6E90\u6C60\u5B58\u5728\u5373\u5C06\u8D85\u671F\u7684\u8D44\u6E90\uFF0C\u8BF7\u53CA\u65F6\u5904\u7406!
resource.warning.expiryDate.tip=\u4EE5\u4E0B\u8D44\u6E90\u5373\u5C06\u8D85\u671F:
resource.warning.resourceNo=\u8D44\u6E90\u7F16\u53F7
resource.warning.deviceType=\u8BBE\u5907\u7C7B\u578B
resource.warning.lowWaterLevel=\u6C34\u4F4D\u503C
resource.warning.availableQuantity=\u53EF\u7528\u8D44\u6E90\u6570\u91CF
resource.warning.expiryDate=\u6709\u6548\u671F
resource.warning.expiryDateBefore=\u6709\u6548\u9884\u8B66\u671F\u9650\uFF08\u5929\uFF09

resource.warning.import.check.fail=\u8D44\u6E90\u9884\u8B66\u7EF4\u62A4\u5BFC\u5165\u6821\u9A8C\u5931\u8D25
resource.warning.required.one=\u6C34\u4F4D\u503C/\u9884\u8B66\u5929\u6570\u5FC5\u9009\u4E00;
resource.warning.resourceNo.notEmpty=\u8D44\u6E90\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
resource.warning.deviceType.notEmpty=\u8BBE\u5907\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
resource.warning.resource.exist=\u8D44\u6E90\u7F16\u53F7\u5DF2\u5B58\u5728
resource.warning.device.exist=\u8BBE\u5907\u578B\u53F7\u5DF2\u5B58\u5728
resource.warning.resource.device.noExist=\u8D44\u6E90\u7C7B\u578B\u4E0E\u8BBE\u5907\u578B\u53F7\u5173\u7CFB\u4E0D\u5B58\u5728
resource.warning.import.max=\u5BFC\u5165\u6700\u5927\u652F\u63011000\u6761
resource.warning.type.notEmpty=\u9884\u8B66\u7EF4\u5EA6\u4E0D\u80FD\u4E3A\u7A7A;
resource.warning.device.isNUll=\u8BBE\u5907\u578B\u53F7\u4E0D\u5B58\u5728;
resource.warning.resource.isNUll=\u8D44\u6E90\u7C7B\u578B\u4E0D\u5B58\u5728;
resource.warning.water.level.isNumeric=\u6C34\u4F4D\u503C\u5FC5\u987B\u662F\u6B63\u6574\u6570;
resource.warning.expiry.date.isNumeric=\u6709\u6548\u9884\u8B66\u5929\u6570\u5FC5\u987B\u662F\u6B63\u6574\u6570;
resource.warning.excel.resource.isSame=excel\u4E2D\u5B58\u5728\u76F8\u540C\u7684\u8D44\u6E90\u7F16\u53F7;
resource.warning.excel.device.isSame=excel\u4E2D\u5B58\u5728\u76F8\u540C\u7684\u8BBE\u5907\u7C7B\u578B;
resource.warning.database.resourceType.device.isSame=\u5DF2\u7EF4\u62A4\u8D44\u6E90\u7F16\u53F7\u76F8\u540C\u7684\u8BBE\u5907\u578B\u53F7,\u4E0D\u5141\u8BB8\u91CD\u590D\u5F55\u5165;
resource.warning.database.device.isMultiple=\u6570\u636E\u5E93\u5B58\u5728\u591A\u4E2A\u76F8\u540C\u8BBE\u5907\u578B\u53F7;
resource.warning.database.device.type.isSame={0}\u8D44\u6E90\u7F16\u53F7\u5BF9\u5E94\u7684\u8BBE\u5907\u7C7B\u578B{1}\u5DF2\u7EF4\u62A4\u9884\u8B66\u89C4\u5219,\u4E0D\u5141\u8BB8\u91CD\u590D\u5F55\u5165;
resource.warning.database.resource.same.device.different=\u5DF2\u7EF4\u62A4\u76F8\u540C\u8D44\u6E90\u7F16\u53F7\u4E0D\u540C\u8BBE\u5907\u578B\u53F7,\u4E0D\u5141\u8BB8\u91CD\u590D\u5F55\u5165;
resource.warning.database.resource.isMultiple=\u6570\u636E\u5E93\u5B58\u5728\u591A\u4E2A\u76F8\u540C\u8D44\u6E90\u7F16\u53F7;
resource.warning.database.resource.isEmpty=\u8D44\u6E90\u7F16\u53F7\u6570\u636E\u5E93\u4E0D\u5B58\u5728;
resource.warning.database.resource.device.isEmpty=\u8D44\u6E90\u7F16\u53F7\u5BF9\u5E94\u8BBE\u5907\u578B\u53F7\u6570\u636E\u5E93\u4E0D\u5B58\u5728;
resource.warning.database.resource.device.mismatch=\u8D44\u6E90\u7F16\u53F7\u548C\u8BBE\u5907\u578B\u53F7\u4E0D\u5339\u914D;
resource.warning.type.notSupport=\u7CFB\u7EDF\u4E0D\u652F\u6301\u6B64\u9884\u8B66\u7EF4\u5EA6

network.license.cert.name=\u8BC1\u4E66\u540D\u79F0\u4EC5\u652F\u6301\u201C\u8FDB\u7F51\u8BD5\u7528\u201D\u3001\u201C\u8FDB\u7F51\u8BB8\u53EF\u201D;
network.license.resource.type=\u8BA4\u8BC1\u7C7B\u522B\u4EC5\u652F\u6301\u201C\u5165\u7F51\u8BB8\u53EF\u201D;
network.license.file.name.error=\u6587\u4EF6\u540D\u9519\u8BEF\uFF0C\u5FC5\u987B\u4E3A14\u4F4D\u8D44\u6E90\u7F16\u53F7\u5F00\u5934\u7684txt\u6587\u4EF6\uFF01
network.license.file.max=\u6700\u5927\u4EC5\u652F\u630130\u4E07\u884C\uFF01
network.license.file.import=\u5BFC\u5165\u6587\u4EF6\u5F02\u5E38\uFF01
network.license.file.error=\u89E3\u6790\u9519\u8BEF\uFF01
network.license.sign.exits=\u7535\u5B50\u6807\u7B7E\u6587\u6863\u5185\u8D44\u6E90\u5DF2\u5728\u7CFB\u7EDF\u5B58\u5728\uFF0C\u8BF7\u52FF\u91CD\u590D\u5BFC\u5165!
network.license.sign.file.exits=\u91CD\u590D\uFF0C\u8BF7\u52FF\u91CD\u590D\u5BFC\u5165!
network.license.print.shortage={0}\u53EF\u7528\u8D44\u6E90\u6570\u91CF\u4E0D\u8DB3!
Hard.Cord.Time.Format=\u65E5\u671F\u8F93\u5165\u9519\u8BEF\uFF0C\u683C\u5F0F\u793A\u4F8B: 2022-10-01
get.aps.plan.group.and.model.error=\u83B7\u53D6aps\u8BA1\u5212\u7EC4\u4EE5\u53CA\u673A\u578B\u4FE1\u606F\u9519\u8BEF
get.aps.plan.group.and.model.errors=\u83B7\u53D6aps\u8BA1\u5212\u7EC4\u4EE5\u53CA\u673A\u578B\u4FE1\u606F\u9519\u8BEF,\u9519\u8BEF\u4FE1\u606F:{0}
more.than.max.num=\u8D85\u8FC7\u6700\u5927\u5904\u7406\u6570\u91CF{0}

network.license.binding.isNUll={0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u626B\u63CF\u6B63\u786E\u7684\u6807\u5FD7\u53F7
network.license.binding.allocated={0}\u72B6\u6001\u4E0D\u4E3A\u5DF2\u5206\u914D\uFF0C\u8BF7\u626B\u63CF\u6B63\u786E\u7684\u6807\u5FD7\u53F7
network.license.binding.repeated={0}\u5DF2\u7ED1\u5B9A{1}\u6279\u6587\uFF0C\u4E0D\u80FD\u91CD\u590D\u7ED1\u5B9A
resource.no.available.quantity.not.enough={0}\u8D44\u6E90\u7F16\u53F7\u53EF\u7528\u6570\u91CF\u4E3A{1}\uFF0C\u8BF7\u66F4\u6362\u8D44\u6E90\u7F16\u53F7
current_resource_number_is_operation=\u5F53\u524D\u8D44\u6E90\u7F16\u53F7\u6B63\u5728\u64CD\u4F5C\u4E2D\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5
operation_time_can_not_greater_three_months=\u64CD\u4F5C\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E09\u4E2A\u6708
no_current_barcode_binding_record_found=\u672A\u627E\u5230\u5F53\u524D\u6761\u7801\u7ED1\u5B9A\u8BB0\u5F55
failed_to_get_preview_link=\u83B7\u53D6\u6587\u4EF6\u9884\u89C8\u8FDE\u63A5\u5931\u8D25{0}
failed_to_generate_preview_header=\u751F\u6210\u6587\u4EF6\u9884\u89C8\u9274\u6743\u5934\u4FE1\u606F\u5931\u8D25
approval_comments_cannot_be_empty_when_rejecting=\u62D2\u7EDD\u65F6\u5BA1\u6279\u610F\u89C1\u4E0D\u80FD\u4E3A\u7A7A
query.time.and.billNo.is.null=\u9664\u5F02\u5E38\u5355\u53F7\u5916\u5176\u4ED6\u6761\u4EF6\u8981\u914D\u5408\u767B\u8BB0\u65F6\u95F4\u67E5\u8BE2
query.last.time.and.billNo.is.null=\u9664\u5F02\u5E38\u5355\u53F7\u5916\u5176\u4ED6\u6761\u4EF6\u8981\u914D\u5408\u6700\u540E\u66F4\u65B0\u65F6\u95F4\u67E5\u8BE2
time_interval_more_than_half_year=\u767B\u8BB0\u65F6\u95F4\u67E5\u8BE2\u8303\u56F4\u4E0D\u5F97\u8D85\u8FC7\u534A\u5E74
last_time_interval_more_than_half_year=\u6700\u540E\u66F4\u65B0\u65F6\u95F4\u67E5\u8BE2\u8303\u56F4\u4E0D\u5F97\u8D85\u8FC7\u534A\u5E74
the_person_to_be_handed_over_to_can_not_be_null=\u8F6C\u4EA4\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
the_approval_type_is_incorrect=\u5BA1\u6279\u7C7B\u578B\u4E0D\u5BF9
fail_to_upload_file=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
file.type.illegal=\u6587\u4EF6\u7C7B\u578B\u4E0D\u6B63\u786E\uFF0C\u8BF7\u786E\u8BA4\uFF01
bill_info_input_param_empty=\u5F02\u5E38\u5355\u53F7\u3001\u57FA\u5730\u3001\u90E8\u95E8\u548C\u5904\u7406\u4EBA\u5747\u4E0D\u80FD\u4E3A\u7A7A
please_maintain_at_least_one=\u8BF7\u7EF4\u62A4\u81F3\u5C11\u4E00\u6761\u5355\u636E\u8BE6\u60C5
input_sn_is_null_or_duplicate=\u5F55\u5165\u8BE6\u60C5\u4E2D\u7684\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A\u4E14\u4E0D\u80FD\u91CD\u590D\uFF0C\u8BF7\u786E\u8BA4
type_or_dec_of_sn_is_null=\u8BF7\u5C06\u6761\u7801{0}\u7684\u5F02\u5E38\u7C7B\u578B\u548C\u5F02\u5E38\u63CF\u8FF0\u8865\u5145\u5B8C\u6574
qty.of.barcode.can.not.be.null=\u6761\u7801{0}\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
no.corresponding.documents.obtained.in.the.approval.center=\u672A\u5728\u5BA1\u6279\u4E2D\u5FC3\u83B7\u53D6\u5230\u76F8\u5E94\u5355\u636E
external.type.is.null=\u6761\u7801{0}\u4EA7\u54C1\u5927\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
style.is.null=\u6761\u7801{0}\u673A\u578B\u4E0D\u80FD\u4E3A\u7A7A
operation_time_can_not_greater_ont_months=\u7ED1\u5B9A\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E00\u4E2A\u6708
operation_time_can_not=\u7ED1\u5B9A\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A

resource_use_no_is_empty_or_status_error=\u8D44\u6E90\u6807\u53F7\u4E0D\u5B58\u5728\u6216\u72B6\u6001\u5F02\u5E38
resource_use_barcode_type_not_correct=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u5E8F\u53F7\u7C7B\u578B
resource_use_num_is_empty=\u8D44\u6E90\u53F7\u4E0D\u5B58\u5728
resource_use_no_is_diff=\u6240\u5C5E\u8D44\u6E90\u7F16\u53F7\u4E0E\u8F93\u5165\u8D44\u6E90\u7F16\u53F7\u4E0D\u4E00\u81F4
resource_use_status_not_allow_import=\u8D44\u6E90\u72B6\u6001\u4E3A{0}\u4E0D\u5141\u8BB8\u8FDB\u884C\u8865\u5F55
resource_use_num_exist_same=\u5B58\u5728\u76F8\u540C\u7684\u8D44\u6E90\u53F7
resource_use_barcode_no_same=\u540C\u4E00\u4EA7\u54C1SN/MAC\u4E0D\u5141\u8BB8\u4F7F\u7528\u540C\u4E00\u8D44\u6E90\u7F16\u53F7\u5185\u7684\u8D44\u6E90\u53F7
resource_use_barcode_type_is_diff=\u6240\u5C5E\u5E8F\u53F7\u7C7B\u578B\u4E0E\u8F93\u5165\u5E8F\u53F7\u7C7B\u578B\u4E0D\u4E00\u81F4
resource_use_data_option_error=\u6570\u636E\u5904\u7406\u5F02\u5E38
resource_use_import_error=\u6587\u4EF6\u5BFC\u5165\u5F02\u5E38:
max.export.is.once=\u5355\u6B21\u6700\u591A\u53EA\u80FD\u5BFC\u51FA1000000\u6761\uFF01
exceed.max.export.count=\u8D85\u8FC7\u6700\u5927\u5BFC\u51FA\u6570\u91CF:{0}
resource_use_import_file_is_empty=\u8D44\u6E90\u4F7F\u7528\u8BB0\u5F55\u5BFC\u5165\u6587\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
scrap.num.bigger.than.data.num=\u8F93\u5165\u62A5\u5E9F\u6570\u91CF\u8D85\u8FC7\u8D44\u6E90\u7F16\u53F7\u4E0B\u8D44\u6E90\u53F7\u53EF\u62A5\u5E9F\u6570\u91CF{0}
scrap.num.over.limit=\u8D44\u6E90\u62A5\u5E9F\u5355\u6B21\u6700\u591A\u53EA\u80FD\u62A5\u5E9F30W\u6761\u6570\u636E
query.resource.scrap.params.error=\u8D44\u6E90\u62A5\u5E9F\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u64CD\u4F5C\u4EBA\u9700\u8981\u548C\u64CD\u4F5C\u65F6\u95F4\u7ED3\u5408\u67E5\u8BE2
query.resource.scrap.date.range.over.year=\u64CD\u4F5C\u65F6\u95F4\u8303\u56F4\u4E0D\u80FD\u8D85\u8FC7\u4E00\u5E74
network.license.print.num.max=\u6253\u5370\u8D44\u6E90\u53F7\u603B\u91CF\u8D85\u8FC71000
network.license.print.sn.max=\u6253\u5370SN\u6570\u91CF\u8D85\u8FC71000
network.license.print.same=\u8D44\u6E90\u53F7\u4E0ESN\u4E0D\u80FD\u540C\u65F6\u6253\u5370
cf.user.menu.max.count=\u5F53\u524D\u5DE5\u5382\u4E0B\u5230\u8FBE\u83DC\u5355\u6700\u5927\u6536\u85CF\u6570:{0},\u8BF7\u5148\u5220\u9664\u90E8\u5206\u518D\u6536\u85CF\uFF01
operation_in_progress_please_wait=\u6B63\u5728\u64CD\u4F5C\u4E2D\uFF0C\u8BF7\u7A0D\u540E
failed_to_call_b2b_interface=\u8C03B2B\u63A5\u53E3\u5931\u8D25,\u9519\u8BEF\u4FE1\u606F{0}
ai_question_not_found=\u60A8\u54A8\u8BE2\u7684\u95EE\u9898\u6CA1\u6709\u627E\u5230\uFF0C\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u4EBA\u5458
product.code.not.same.error=\u8F93\u5165\u6599\u5355\u4E0E\u5199\u7247\u4FE1\u606F\u4E2D\u7684\u6599\u5355\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
product.code.length.error=\u6599\u5355\u4EE3\u7801\u957F\u5EA6\u5E94\u4E3A12\u4F4D\u621615\u4F4D\uFF0C\u8BF7\u786E\u8BA4\uFF01
mds.in.program.error=MDS\u89E6\u53D1iMES\u524D\u52A0\u5DE5\u6570\u636E\u63A5\u53E3\u5F02\u5E38\uFF0C\u5177\u4F53\u5F02\u5E38\u4FE1\u606F\u4E3A\uFF1A{0}
product.code.num.over.ten=\u6599\u5355\u5BF9\u5E9415\u4F4D\u6599\u5355\u8D85\u8FC710\u6761\uFF0C\u65E0\u6CD5\u6267\u884C\uFF0C\u8BF7\u786E\u8BA4\uFF01
call.barCode.center.expandQuery.barCode.falied=\u8C03\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6761\u7801\u6269\u5C55\u4FE1\u606F\u5931\u8D25:{0}
data.delete.refresh.page=\u6570\u636E\u5DF2\u7ECF\u88AB\u5220\u9664,\u8BF7\u5237\u65B0\u9875\u9762!
customer.param.table.error=\u5BA2\u6237\u53C2\u6570{0}\u914D\u7F6E\u4FE1\u606F\u8868{1}\u6709\u8BEF\u8BF7\u786E\u8BA4\uFF01
dqas.error=\u4E2D\u8BD5\u63A5\u53E3\u8C03\u7528\u5F02\u5E38,\u63A5\u53E3\u4FE1\u606F:{0}
xp.auto.sync.failed.title.one=MDS\u89E6\u53D1\u5199\u7247\u4FE1\u606F\u540C\u6B65\u5931\u8D25,\u6599\u5355:{0},\u8BF7\u786E\u8BA4\u540E\u8FDB\u884C\u4EBA\u5DE5\u7EF4\u62A4
xp.auto.sync.failed.title.two=\u5199\u7247\u524D\u52A0\u5DE5\u81EA\u52A8\u7EF4\u62A4\u5931\u8D25\uFF1A\u6599\u5355:{0},\u5355\u677F\u540D\u79F0:{1},PCB\u7248\u672C:{2},\u8BF7\u786E\u8BA4\u540E\u8FDB\u884C\u4EBA\u5DE5\u7EF4\u62A4
xp.auto.sync.failed.title.three=\u5199\u7247\u524D\u52A0\u5DE5\u81EA\u52A8\u7EF4\u62A4\u5931\u8D25\uFF1A\u6599\u5355:{0},\u5355\u677F\u540D\u79F0:{1},PCB\u7248\u672C:{2},\u8BF7\u786E\u8BA4\u540E\u8FDB\u884C\u4EBA\u5DE5\u7EF4\u62A4,\u5E76\u6267\u884CBOM\u5206\u9636\u3002
xp.auto.sync.failed.msg.two= {0}\u6599\u5355\u524D\u52A0\u5DE5\u5199\u7247\u6570\u636E\u5728UTS\u6216PCB\u5347\u7EA7\u4F46\u7814\u53D1\u672A\u542F\u7528
xp.auto.sync.failed.msg.three={0}\u6599\u5355\u5728MDS\u65E0\u5199\u7247\u4FE1\u606F
xp.auto.sync.failed.msg.four={0}\u6599\u5355\u672A\u67E5\u5230\u4F4D\u53F7\u4FE1\u606F,\u65E0\u6CD5\u540C\u6B65\u5199\u7247\u4FE1\u606F,\u8BF7\u786E\u8BA4\u6267\u884CBOM\u4F4D\u53F7\u89E3\u6790\u540E\u91CD\u65B0\u64CD\u4F5C
xp.auto.sync.failed.msg=\u5199\u7247\u524D\u52A0\u5DE5\u81EA\u52A8\u7EF4\u62A4\u5931\u8D25,\u5F02\u5E38\u539F\u56E0: {0}
pcbversion.not.exist=\u5355\u677F\u7248\u672C\u4E0D\u5B58\u5728
resource.num.max.one.hundred=\u8D44\u6E90\u7F16\u53F7\u6279\u91CF\u67E5\u8BE2\u6700\u591A\u652F\u6301100\u6761
relationship.verify.not.pass=\u9886\u7528\u5173\u7CFB\u65B0\u589E/\u4FEE\u6539\u53C2\u6570\u6821\u9A8C\u4E0D\u901A\u8FC7\uFF0C\u5177\u4F53\u539F\u56E0\u4E3A\uFF1A{0}
change.order.no.data.null=\u672A\u83B7\u53D6\u5230\u53D8\u66F4\u5355\u53F7\u5BF9\u5E94\u6570\u636E
params.lacking=\u53C2\u6570\u7F3A\u5931
not.found.item.info=\u672A\u83B7\u53D6\u5230\u7269\u6599\u4FE1\u606F
the_model_code_of_the_resource_number_is_empty=\u8D44\u6E90\u7F16\u53F7{0}\u578B\u53F7\u7F16\u7801\u4E3A\u7A7A\uFF0C\u4E0D\u80FD\u6253\u5370
Please_enter_the_question_content=\u8BF7\u8F93\u5165\u63D0\u95EE\u5185\u5BB9
bom.item.use.account = \u7269\u6599\u4EE3\u7801 {0},Bom\u5206\u9636\u7528\u91CF\uFF1A{1},Bom\u7528\u91CF:{2}
bom.exception.message= \u6599\u5355\u4EE3\u7801 {0} BOM\u5206\u9636\u5F02\u5E38 {1} \u8BF7\u68C0\u67E5\u8BE5\u6599\u5355,\u6599\u5355\u7EA7\u524D\u52A0\u5DE5\u6570\u636E\uFF01
please.input.one.condition=\u8BF7\u81F3\u5C11\u8F93\u5165\u4E00\u4E2A\u67E5\u8BE2\u6761\u4EF6
the_model_code_is_empty= \u6A21\u578B\u7F16\u7801\u4E3A\u7A7A\uFF0C\u4E0D\u80FD\u5206\u914D

resource.no.rule.invalid=\u8D44\u6E90\u53F7\u89C4\u5219\u4E0D\u662F\u6709\u6548\u7684\u6B63\u5219\u8868\u8FBE\u5F0F\uFF0C\u8BF7\u786E\u8BA4\uFF01
resource.step.is.different=\u8F93\u5165\u7684\u5355\u53F0\u7528\u91CF\u4E0E\u7533\u8BF7\u5355\u7684\u5355\u53F0\u7528\u91CF\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\uFF01
params.rule.error=\u53C2\u6570\u89C4\u5219\u8F93\u5165\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\uFF01
no.mac.available.resource=\u65E0\u53EF\u7528MAC\u8D44\u6E90
no.gpon.sn.available.resource=\u65E0\u53EF\u7528GPON-SN\u8D44\u6E90
no.available.resource=\u65E0\u53EF\u7528{0}\u8D44\u6E90
random.numbers.must.greater.than.or.equal.3=\u968F\u673A\u4F4D\u6570\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E3
item.no.is.invalid=\u7269\u6599\u4EE3\u7801\uFF1A{0} \u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
task.is.generating=\u8BE5\u4EFB\u52A1\u4E2A\u53C2\u6B63\u5728\u751F\u6210\u4E2D
apply.qty.is.exceed.max=\u7533\u8BF7\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7{0}
emp.no.has.undone.task=\u8BE5\u5DE5\u53F7\u6709\u672A\u5B8C\u6210\u7684\u4EFB\u52A1\uFF0C\u8BF7\u5148\u5B8C\u6210\uFF01
aoi.img.not.exist=\u6599\u5355\u5BF9\u5E94AOI\u56FE\u7247\u4E0D\u5B58\u5728
eigen.value.exceed.max=\u79FB\u52A8SN\u7279\u5F81\u503C[{0}]\u5269\u4F59\u53EF\u7528\u6570\u503C\u4E0D\u8DB3\uFF0C\u5DF2\u7D2F\u8BA1\u503C\u4E3A{1}
sub.customer.config.lost=\u5B50\u516C\u53F8{0}\u6CA1\u6709\u914D\u7F6E\u5BA2\u6237\u540D\u79F0\u914D\u7F6E\u9879{1}
ucs.login.error=UCS\u7CFB\u7EDF\u767B\u5F55\u5F02\u5E38\uFF0C\u8BF7\u786E\u8BA4\uFF01
aps.task.no.is.null=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
task.no.info.is.null=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u5728\u4E2D\u5FC3\u5DE5\u5382\u672A\u627E\u5230\u5BF9\u5E94\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
aps.task.no.info.factory.id.is.null=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u5BF9\u5E94\u7684\u4EFB\u52A1\u4FE1\u606F\u4E2D\u5DE5\u5382id\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
kafka.msg.save.database.derivativeCode.update=\u884D\u751F\u7801kafka\u6D88\u606F \u6D88\u8D39\u5931\u8D25\uFF0C\u5DF2\u7ECF\u5B58\u5165\u672C\u5730\u6D88\u606F\u8868\uFF0C\u8BF7\u53CA\u65F6\u5206\u6790\u5904\u7406,\u6D88\u606Fid:{0}
task.no.info.not.exist=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7{0}\u5728\u4E2D\u5FC3\u5DE5\u5382\u672A\u627E\u5230\u5BF9\u5E94\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\uFF01
task.no.transferring=\u8BA1\u5212\u8DDF\u8E2A\u5355\u53F7\u5DF2\u4EA7\u751F\u6216\u8005\u6B63\u5728\u4EA7\u751F\u8C03\u62E8\u5355\u6216\u8005\u5957\u6599\u5355\uFF0C\u8BF7\u786E\u8BA4\uFF01
no_derivative_code_change_information_found=\u672A\u67E5\u8BE2\u5230APS\u884D\u751F\u7801\u53D8\u66F4\u4FE1\u606F
original_bom_information_not_queried=\u672A\u67E5\u8BE2\u5230\u6599\u5355\u539F\u59CBBOM\u4FE1\u606F
this_task_version_has_already_been_processed=\u6B64\u4EFB\u52A1+\u7248\u672C\u5DF2\u5904\u7406\u8FC7
consent.available.quantity.is.insufficient=\u5165\u7F51\u6279\u6587\u8D44\u6E90\u53F7\u53EF\u7528\u6570\u91CF\u4E0D\u8DB3
write.back.erp.error=\u5DE5\u65F6\u56DE\u5199ERP\u9519\u8BEF\uFF01{0}
net.work.params.must.existing.simultaneously=\u5165\u7F51\u53C2\u6570{NASN,ScramblingCode,NACC}\u5FC5\u987B\u540C\u65F6\u5B58\u5728
variable.does.not.exist=\u53C2\u6570\u53D8\u91CF{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
user.no.permissions = \u5F53\u524D\u7528\u6237\u65E0api\u4FEE\u6539\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u9879\u76EE\u7EC4\u5904\u7406!
param_zte_code_not_blank=\u5165\u53C2ZTE\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
pack_list_already_existed=\u5BA2\u6237\u540D\u79F0{0}\uFF0C\u5BA2\u6237\u90E8\u4EF6\u7C7B\u578B{1}\u7BB1\u5355\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4!
failed_to_obtain_ucs_public_key=\u83B7\u53D6ucs\u516C\u94A5\u5931\u8D25
cpqd.instance.no.lost=CPQD \u4EA7\u54C1\u5B9E\u4F8B:{0} FIXBOM\u7F16\u7801\u7F3A\u5931!
icc.mbom.lost=ICC \u5BA2\u6237{0} \u4E8C/\u4E09\u6BB5\u7801{1} Mbom\u6570\u636E\u7F3A\u5931!
pdm.mbom.lost=PDM \u6599\u5355{0} \u7269\u6599\u6E05\u5355\u7F3A\u5931\uFF01
pdm.mbom.item.icc.mbom.lost=PDM \u6599\u5355{0}\u4E2D\u7269\u6599{1}\u5728ICC Mbom\u4E2D\u7F3A\u5931\uFF01
task.fix.bom.error=\u4EFB\u52A1{0} fixBom \u751F\u6210\u5931\u8D25\u539F\u56E0:{1}.
task.fix.bom.subject=\u4EFB\u52A1FixBom\u751F\u6210\u5931\u8D25\u8BF7\u5173\u6CE8!
task.list.not.blank=\u4EFB\u52A1\u53F7\u6570\u7EC4\u4E0D\u80FD\u4E3A\u7A7A!
fix.bom.detail.lost=\u4EFB\u52A1{0} FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
fix.bom.incomplete={0}\u4EFB\u52A1\u672A\u83B7\u53D6\u5230\u5BA2\u6237\u6307\u4EE4\u4FE1\u606F\uFF0C\u4E0D\u5141\u8BB8\u53D1\u653E\u81F3\u672C\u5730\u5DE5\u5382\u751F\u4EA7
split.not.allowed=\u4EFB\u52A1{0}\u4E0D\u5141\u8BB8\u62C6\u5206
customer.to.lookupType.config.lost=\u672A\u914D\u7F6E\u5BA2\u6237{0}\u4E0E\u6570\u636E\u5B57\u5178\u7F16\u53F7\u7684\u6620\u5C04\u5173\u7CFB
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
get.url.null=\u672A\u83B7\u53D6\u6570\u636E\u5B57\u5178:{0}\u7684URL
erpstock.null=\u672A\u83B7\u53D6\u4ED3\u5E93\u4EE3\u7801:{0}\u7684ERP\u5B50\u5E93\u5B58
level.zero.fix.bom.detail.error=0 \u5C42 FixBom \u6570\u636E\u7F3A\u5931\u8BF7\u786E\u8BA4!
zte.code.fixbomid.is.null=ZTE\u7269\u6599\u4EE3\u7801 {0} \u4E0D\u5B58\u5728\u5BF9\u5E94fixbomid
delivery.feedback.is.must=\u5FC5\u987B\u9009\u62E9\u53CD\u9988\u65F6\u95F4
max.search.tasks=\u6700\u5927\u67E5\u8BE2:{0}\u4E2A\u4EFB\u52A1\u53F7
sn.data.size.over=\u6570\u636E\u91CF\u4E0D\u80FD\u8D85\u8FC7{0}\u6761
task.no.repeat=\u5BFC\u5165\u7684excel\u4E2D\u4EFB\u52A1\u53F7\u4E0D\u80FD\u91CD\u590D\uFF01\u8BF7\u7528\u6237\u68C0\u67E5\u540E\u4E0A\u4F20\uFF01
abnormal.no.repeat=\u5BFC\u5165\u7684excel\u4E2D\u5EF6\u671F\u7F16\u53F7\u4E0D\u80FD\u91CD\u590D\uFF01\u8BF7\u7528\u6237\u68C0\u67E5\u540E\u4E0A\u4F20\uFF01
submit.failed=\u63D0\u4EA4\u5931\u8D25,\u539F\u56E0:{0}
task.no.not.empty=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A
task.no.status=\u4EFB\u52A1\u53F7\u72B6\u6001\u5FC5\u987B\u4E3A\u5DF2\u5F00\u5DE5\u3001\u5DF2\u6392\u4EA7\u548C\u5DF2\u53D1\u653E
delivery.feedback.date.not.empty=\u9884\u8BA1\u5B8C\u5DE5\u65E5\u671F\u3001\u5382\u5546\u81EA\u4F9B\u6599\u9884\u8BA1\u9F50\u5957\u65E5\u671F\u3001\u5168\u90E8\u7269\u6599\u9884\u8BA1\u9F50\u5957\u65E5\u671F\u3001\u5382\u5546\u81EA\u4F9B\u6599\u5B9E\u9645\u9F50\u5957\u65E5\u671F\u3001\u5168\u90E8\u7269\u6599\u5B9E\u9645\u9F50\u5957\u65E5\u671F\u3001\u9884\u8BA1\u6295\u4EA7\u65E5\u671F\u3001\u5B9E\u9645\u6295\u4EA7\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
task.delayed.reason.not.empty=\u4EFB\u52A1\u53F7\u5EF6\u671F\uFF0C\u9700\u8981\u5FC5\u586B\u5EF6\u671F\u539F\u56E0\u7B49
custom.no.not.alibaba=\u5FC5\u987B\u662F\u963F\u91CC\u4EFB\u52A1
delivery.feedback.no.receiver=\u751F\u4EA7\u4EA4\u671F\u53CD\u9988\u90AE\u4EF6\u914D\u7F6E\u76EE\u5F55\u4EE3\u7801:{0},\u6CA1\u6709\u7EF4\u62A4\u63A5\u6536\u4EBA
customer.code.name.not.match=\u6CA1\u6709\u7EF4\u62A4\u5BA2\u6237\u7F16\u7801:{0} \u4E0E\u540D\u79F0\u7684\u5BF9\u5E94\u5173\u7CFB
task.no.not.match.record=\u4EFB\u52A1\u53F7:{0}\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u751F\u4EA7\u4EA4\u671F\u53CD\u9988\u8BB0\u5F55customer.to.lookupType.config.lost=\u672A\u914D\u7F6E\u5BA2\u6237{0}\u4E0E\u6570\u636E\u5B57\u5178\u7F16\u53F7\u7684\u6620\u5C04\u5173\u7CFB
failed_to_call_mds_interface=\u8C03MDS\u63A5\u53E3\u5931\u8D25:{0}