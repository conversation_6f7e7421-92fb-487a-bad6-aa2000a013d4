RetCode.Success=\u64CD\u4F5C\u6210\u529F
RetCode.ServerError=\u670D\u52A1\u5668\u9519\u8BEF
RetCode.AuthFailed=\u8BA4\u8BC1\u5931\u8D25
RetCode.PermissionDenied=\u6CA1\u6709\u6743\u9650
RetCode.ValidationError=\u9A8C\u8BC1\u5931\u8D25
RetCode.BusinessError=\u4E1A\u52A1\u5F02\u5E38
customize.msg={0}
task.no.is.batch=\u8F93\u5165\u4EFB\u52A1\u53F7\u4E0D\u80FD\u652F\u6301\u6279\u91CF\u67E5\u8BE2
job.stop.sysn=\u540C\u6B65\u5931\u8D25

empty.input.contract.eg=\u5408\u540C\u53F7\u3001\u7269\u6599\u4EE3\u7801\u3001\u7269\u6599\u540D\u79F0\u3001\u7269\u6599\u6761\u7801\uFF0C\u81F3\u5C11\u8F93\u5165\u4E00\u4E2A\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
only.itemname=\u53EA\u8F93\u5165\u4E86\u7269\u6599\u540D\u79F0\uFF0C\u8BF7\u8F93\u5165\u5176\u5B83\u7CBE\u786E\u67E5\u8BE2\u6761\u4EF6\u6216\u53D1\u8D27\u65E5\u671F\u8303\u56F4\u8BBE\u5B9A\u523010\u5929\u4EE5\u5185


entity.trace.input.is.null=ERP\u6392\u4EA7\u5F00\u59CB\u65E5\u3001ERP\u6392\u4EA7\u7ED3\u675F\u65E5\u3001\u8BA1\u5212\u7EC4\u3001\u5B50\u72B6\u6001\u90FD\u5FC5\u987B\u8F93\u5165\uFF0C\u8BF7\u786E\u8BA4
erp.start.time.and.end.time=ERP\u6392\u4EA7\u5F00\u59CB\u65E5\u3001ERP\u6392\u4EA7\u7ED3\u675F\u65E5\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u8D85\u8FC7{0}\u5929
pagesize.is.empty=\u8F93\u5165\u67E5\u8BE2\u9875\u7801\u6216\u8005\u6BCF\u9875\u5C55\u793A\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4

Please.Input.WareHouse=\u8BF7\u8F93\u5165\u4ED3\u5E93
Please.Input.OrderKey.WaveKey=\u8BA2\u5355\u53F7,\u6CE2\u6B21\u53F7,\u81F3\u5C11\u8981\u6709\u4E00\u4E2A\u8F93\u5165\u6761\u4EF6
Please.Input.Id=\u8BF7\u8F93\u5165\u7BB1\u53F7
Please.Input.OrderKey.ExternalOrderKey2.WaveKey.Id=SO\u5355\u53F7,\u5916\u90E8\u5355\u53F72,\u6CE2\u6B21\u53F7,\u843D\u653EID,\u81F3\u5C11\u8981\u6709\u4E00\u4E2A\u8F93\u5165\u6761\u4EF6
Please.Input.OrderKey.ExternalOrderKey2=\u8BA2\u5355\u53F7,\u5916\u90E8\u8BA2\u5355\u53F72,\u81F3\u5C11\u8981\u6709\u4E00\u4E2A\u8F93\u5165\u6761\u4EF6
Please.Import.Excel.NoMoreThan.Contractnumber.Fifty=\u5408\u540C\u53F7\u8D85\u8FC750\u884C\uFF0C\u8BF7\u68C0\u67E5\u8C22\u8C22\uFF01
Please.Import.Excel.NoMoreThan.Doid.FiveHundred=\u53D1\u8D27\u6307\u4EE4\u53F7\u8D85\u8FC7500\u884C\uFF0C\u8BF7\u68C0\u67E5\u8C22\u8C22\uFF01
Please.Import.Excel.NoMoreThan.BillNumber.FiveHundred=\u88C5\u7BB1\u5355\u53F7\u8D85\u8FC7500\u884C\uFF0C\u8BF7\u68C0\u67E5\u8C22\u8C22\uFF01
Please.Check.Import.File.Type.Excel=\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u4F7F\u7528excel\u5BFC\u5165\uFF01
board.not.belong=\u8BE5\u5355\u677F\u4E0D\u5C5E\u4E8E\u672C\u90E8\u4EF6
module.barcode.not.exists=\u8BE5\u6A21\u5757\u6761\u7801\u4E0D\u5B58\u5728
workorder.sourcesys.is.null=\u6307\u4EE4\u6765\u6E90\u4E0D\u80FD\u4E3A\u7A7A
main.sn.prodplan.is.null=\u4E3B\u6761\u7801\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
sub.sn.prodplan.is.null = \u5B50\u6761\u7801\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
sub.sn.is.null = \u5B50\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
main.sn.is.null=  \u4E3B\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A
get_board_assembly_relationship_error=\u83B7\u53D6\u4E3B\u6761\u7801\u5B50\u6761\u7801\u63A5\u53E3\u5F02\u5E38:


data.wb.call.param.miss=\u8C03\u7528\u6570\u636E\u56DE\u5199\u670D\u52A1\u7F3A\u5C11 {0} \u53C2\u6570
emp.no.is.null=\u5DE5\u53F7\u4E3A\u7A7A
time.format.error=\u65F6\u95F4\u683C\u5F0F\u9519\u8BEF
sfc.return.cur.value.is.null=sfc\u8FD4\u56DE\u5F53\u524D\u6761\u7801\u503C\u4E3A\u7A7A
sfc.return.cur.value.is.not.number=sfc\u8FD4\u56DE\u5F53\u524D\u6761\u7801\u503C\u975E\u6570\u5B57
sfc.return.cur.value.is.more.than.max=sfc\u8FD4\u56DE\u5F53\u524D\u6761\u7801\u503C\u8D85\u8FC7\u6700\u5927\u503C
Please.Input.query.condition=\u8BF7\u8F93\u5165\u67E5\u8BE2\u6761\u4EF6
Please.Input.BillNumber=\u8BF7\u8F93\u5165\u7BB1\u53F7
Query.More.BillNumber=\u9650\u5B9A\u67E5\u8BE2100\u4E2A\u7BB1\u53F7\uFF0C\u7BB1\u53F7\u8D85\u51FA
item.id.found.error=\u672A\u627E\u5230itemId,erp\u540C\u6B65\u5931\u8D25
wip.move.mtl.insert.error = ZTE_WIP_MOVE_MTL_TXN \u63D2\u5165\u5931\u8D25
mrp.wip.issue.insert.eror = ZTE_MRP_WIP_ISSUE \u63D2\u5165\u5931\u8D25

entitySiteCode.list.length.should.less.10  = \u8F93\u5165\u7684\u4EFB\u52A1\u53F7\u7AD9\u70B9\u7F16\u7801\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC710
task.no.is.null=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A
update_step_stock_locked=\u66F4\u65B0STEP\u6761\u7801\u548C\u7269\u6599\u5E93\u5B58\u6B63\u5728\u6267\u884C
delete_param_error=\u5220\u9664\u51E0\u4E2A\u6708\u4E4B\u524D\u6708\u4EFD\u53C2\u6570\u5FC5\u987B\u8981\u4E3A\u975E\u8D1F\u6570
itembarcode_stockno_is_null=\u6761\u7801\uFF0C\u5728\u9014\u5E93\u5B50\u5E93\u5B58,\u7EBF\u8FB9\u4ED3ID,\u4E0D\u80FD\u4E3A\u7A7A
itembarcode_not_exist=\u6761\u7801\u5728\u9014\u5E93\u5E93\u5B58\u4E0D\u5B58\u5728

boxNo.no.is.null=\u7BB1\u53F7\u4E0D\u80FD\u4E3A\u7A7A
itemType.no.is.null=\u7269\u6599\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
itemName.no.is.null=\u7269\u6599\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
itemCode.no.is.null=\u7269\u6599\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
MFGSITE.ID.IS.NULL=\u751F\u4EA7\u7AD9\u70B9Id\u4E0D\u80FD\u4E3A\u7A7A
CREATE.BARCODE.QTY.BETWEEN.1.AND.50=\u6253\u5370\u6761\u7801\u6570\u91CF\u5E94\u8BE5\u5927\u4E8E0\u5C0F\u4E8E51
SEARCH.IS.NULL.BY.ENTITYNAME.MFGSITEID.ITEMCODE.LAYER1.LAYER4=\u6309\u4EFB\u52A1\u53F7\u548C\u7AD9\u70B9ID\u67E5\u8BE21\u5C42\u30014\u5C42\u7269\u6599\u4EE3\u7801\u65F6\u4E3A\u7A7A
CREATE.BARCODE.FAILURE=\u751F\u6210\u6761\u7801\u5931\u8D25
PTO.ITEMCODE.IS_NOT.PDVM=\u4E00\u5C42\u7269\u6599\u4E0D\u662F\u6574\u673A
FOURE.LAYER.ITEMCODE.DO.NOT.MARK=\u56DB\u5C42\u7269\u6599\u672A\u6807\u5B9A
data.is.null=\u672A\u8F93\u5165\u6570\u636E
month.compute.time=\u6708\u7ED3\u65F6\u5019\uFF0C\u7981\u6B62\u64CD\u4F5C
barcode.is.no.stock=\u6761\u7801\u5728\u6761\u7801\u5E93\u5B58\u8868\u4E0D\u5B58\u5728
asset.no.stock=\u8D44\u4EA7\u51FA\u5E93\u5355\u8D44\u4EA7\u5728\u5E93\u5B58\u8868\u4E0D\u5B58\u5728
barcode.insufficient.inventory=\u6761\u7801\u5E93\u5B58\u4E0D\u591F
moldassets.insufficient.inventory=\u6A21\u5177\u8D44\u4EA7\u5E93\u5B58\u4E0D\u591F
main.component.prodplanid.is.empty=\u4E3B\u90E8\u4EF6\u6279\u6B21\u4E3A\u7A7A
correct.main.component.prodplanid=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u4E3B\u90E8\u4EF6\u6279\u6B21
can.not.find.data=\u67E5\u627E\u4E0D\u5230\u6570\u636E
main.component.barcode.is.empty=\u4E3B\u90E8\u4EF6\u6761\u7801\u4E3A\u7A7A
main.component.barcode.must.be.number=\u4E3B\u90E8\u4EF6\u6761\u7801\u5FC5\u987B\u90FD\u4E3A\u6570\u5B57
main.component.barcode.bits.is.incorrect=\u4E3B\u90E8\u4EF6\u6761\u7801\u4F20\u5165\u4F4D\u6570\u6709\u8BEF
the.subcomponent.barcode.has.letters=\u5B50\u90E8\u4EF6\u6761\u7801\u5B58\u5728\u5B57\u6BCD\uFF0C\u4E0D\u7B26\u5408\u590D\u9009\u6846\u4F20\u5165\u7684\u503C
the.input.value.of.the.check.box.is.null=\u590D\u9009\u6846\u4F20\u5165\u503C\u4E3A\u7A7A
the.incoming.value.is.null=\u5355\u9009\u6846\u4F20\u5165\u503C\u4E3A\u7A7A
subcomponent.barcode.is.empty=\u5B50\u90E8\u4EF6\u6761\u7801\u4F20\u5165\u503C\u4E3A\u7A7A
operate.person.is.empty=\u64CD\u4F5C\u4EBA\u4F20\u5165\u503C\u4E3A\u7A7A
the.quantity.must.be.number=\u6570\u91CF\u5FC5\u987B\u4E3A\u6570\u5B57
verification.mode.must.be.number=\u9A8C\u8BC1\u65B9\u5F0F\u5FC5\u987B\u4E3A\u6570\u5B57
the.mainpart.batch.not.meet.the.input.value.of.the.check.box=\u4E3B\u90E8\u4EF6\u6279\u6B21\u4E0D\u7B26\u5408\u590D\u9009\u6846\u4F20\u5165\u7684\u503C
the.query.record.id.is.null=\u67E5\u8BE2\u8BB0\u5F55id\u4E3A\u7A7A
the.material.list.code.should.contain.15.characters=\u6599\u5355\u4EE3\u7801\u957F\u5EA6\u5E94\u4E3A15\u4E2A\u5B57\u7B26\uFF0C\u8BF7\u68C0\u67E5\uFF01
no.bom.information.is.found=\u672A\u627E\u5230\u6599\u5355\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5\uFF01
maximum.of.%d.batch.numbers.are.allowed=\u6279\u6B21\u53F7\u6700\u591A\u4E0D\u8D85\u8FC7%d\u4E2A\uFF0C\u8BF7\u68C0\u67E5\uFF01
operate.success=\u64CD\u4F5C\u6210\u529F
the.batch.numbers.do.not.belong.to.the.same.bom=\u6279\u6B21\u53F7\u4E0D\u5C5E\u4E8E\u540C\u4E00\u6599\u5355\uFF0C\u8BF7\u68C0\u67E5\uFF01
prodplanid.does.not.exist=\u6279\u6B21\u53F7[%s]\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\uFF01
params.can.not.empty=\u53C2\u6570\u4E0D\u53EF\u4E3A\u7A7A
params.error=\u53C2\u6570\u5F02\u5E38:{0}
please.enter.a.correct.parameter.and.a.valid.number=\u8BF7\u6B63\u786E\u8F93\u5165\u53C2\u6570\uFF0C\u5408\u6CD5\u6570\u5B57
the.number.of.components.to.be.queried.is.zero=\u67E5\u8BE2\u90E8\u4EF6\u6570\u91CF\u4E3A\u96F6
querying.the.subboard.bound.to.a.component.plan.number.is.0=\u67E5\u8BE2\u90E8\u4EF6\u8BA1\u5212\u7ED1\u5B9A\u5B50\u677F\u6570\u91CF\u4E3A\u96F6
this.barcode.has.technical.modification.requirements=\u8BE5\u6761\u7801\u6709\u6280\u6539\u8981\u6C42\uFF0C\u8BF7\u786E\u8BA4\u6280\u6539\u5355
querying.the.subBoard.bound.to.a.component.plan.info.is.empty=\u8F93\u5165\u7684\u4FE1\u606F,\u7ECF\u67E5\u8BE2\u8BA1\u5212\u7ED1\u5B9A\u5B50\u677F\u4FE1\u606F\u4E3A\u7A7A!
the.number.of.components.queried.is.null=\u67E5\u8BE2\u90E8\u4EF6\u6570\u91CF\u7ED3\u679C\u4E3A\u7A7A!
incorrect.input.information=\u8F93\u5165\u4FE1\u606F\u6709\u9519\uFF0C\u8BF7\u68C0\u67E5\u540E\u91CD\u65B0\u8F93\u5165\uFF08\u67E5\u8BE2\u5B50\u677F\u662F\u5426\u6709\u6536\u6599\u65F6\uFF09!
subboard=\u5B50\u677F
collect.materials.first=\u672A\u6536\u6599,\u8BF7\u5148\u70B9\u6536\u6599\uFF0C\u4FDD\u8BC1\u6240\u6709\u5B50\u677F\u6536\u6599\u5B8C\u6210\uFF01
failed.to.obtain.batch.information=\u83B7\u53D6\u6279\u6B21\u4FE1\u606F\u51FA\u9519\uFF01
the.batch.information.to.be.queried.is.null=\u67E5\u8BE2\u6279\u6B21\u4FE1\u606F\u4E3A\u7A7A,\u4E0D\u80FD\u7ED1\u5B9A
id.is.empty=id\u4E3A\u7A7A
please.enter.the.correct.id.no=\u8BF7\u8F93\u5165\u6B63\u786E\u7684id\u53F7
please.enter.the.correct.legal.number.id.([1-5])=\u8BF7\u8F93\u5165\u6B63\u786E\u7684id\u53F7\uFF08[1-5]\uFF09\u5408\u6CD5\u6570\u5B57
input.params.is.illegal=\u8F93\u5165\u53C2\u6570\u4E0D\u5408\u6CD5,\u8BF7\u68C0\u67E5\u540E\u91CD\u65B0\u8F93\u5165!
failed.to.obtain.the.whitelist.data=\u83B7\u53D6\u767D\u540D\u5355\u6570\u636E\u5931\u8D25!
failed.to.obtain.the.soldering.scan.data=\u83B7\u53D6\u88C5\u710A\u626B\u63CF\u7BA1\u63A7\u6570\u636E\u5931\u8D25
the.relationships.between.mother.boards.and.modules.are.not.scanned=\u672A\u5EFA\u7ACB\u5B50\u6BCD\u5361/\u6A21\u5757\u5BF9\u5E94\u5173\u7CFB\u626B\u63CF\uFF0C\u8BF7\u5148\u5B8C\u6210\u5BF9\u5E94\u626B\u63CF
failed.to.get.the.bimu.of.the.mount.welding.management.control.node.or.the.current.scanning.node=\u83B7\u53D6\u88C5\u710A\u7BA1\u63A7\u8282\u70B9\u6216\u5F53\u524D\u626B\u63CF\u8282\u70B9BIMU\u5931\u8D25
obtain.that.the.bimu.corresponding.to.the.welded.pipe.control.node.is.null=\u83B7\u53D6\u88C5\u710A\u7BA1\u63A7\u8282\u70B9\u5BF9\u5E94\u7684BIMU\u4E3A\u7A7A
obtain.that.the.bimu.corresponding.to.the.current.node.is.null=\u83B7\u53D6\u5F53\u524D\u626B\u63CF\u8282\u70B9\u5BF9\u5E94\u7684BIMU\u4E3A\u7A7A
failed.to.obtain.the.soldering.scan.management.and.control.information=\u83B7\u53D6\u88C5\u710A\u626B\u63CF\u7BA1\u63A7\u4FE1\u606F\u5931\u8D25\uFF01
the.batch.has.been.locked.on.the.scan.node=\u8BE5\u6279\u6B21\u5728\u8BE5\u626B\u63CF\u8282\u70B9\u5DF2\u7ECF\u88AB\u9501\u5B9A\uFF0C\u7981\u6B62\u626B\u63CF\uFF01
there.is.no.material.receiving.record=\u6CA1\u6709\u586B\u6536\u6599\u8BB0\u5F55\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
the.result.is.empty=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
the.imu.is.controled=IMU\u5DF2\u7BA1\u63A7\uFF0C\u8BF7\u786E\u8BA4\u5904\u7406
current.time=\u5F53\u524D\u65F6\u95F4\uFF1A
synchronizing.and.submitting.tables.and.result.tables.to.the.boardonlinezq=\u5B9A\u65F6\u540C\u6B65\u63D0\u4EA4\u8868\u548C\u7ED3\u679C\u8868\u6570\u636E\u5230Board_Online_ZQ\u8868\u51FA\u73B0\u5F02\u5E38\uFF0C\u5F02\u5E38\u4E3A:
the.file.format.is.wrong.please.use.excel.to.import.it=\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u4F7F\u7528excel\u5BFC\u5165\uFF01
query.success=\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u6210\u529F
query.failed=\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u5931\u8D25
download.address=\u4E0B\u8F7D\u5730\u5740
address.may.be=\u6B64\u5730\u5740\u5C06\u5728
automatic.delete=\u540E\u81EA\u52A8\u5220\u9664,\u8BF7\u5C3D\u5FEB\u4E0B\u8F7D
click.download.address=\u70B9\u51FB\u4E0B\u9762\u7684\u4E0B\u8F7D\u5730\u5740\uFF08\u4E0B\u8F7D\u5BFC\u51FA\u6587\u4EF6\uFF09
settop.box.delivery.information.query=\u673A\u9876\u76D2\u53D1\u8D27\u4FE1\u606F\u67E5\u8BE2\uFF0C\u751F\u6210\u67E5\u8BE2\u7ED3\u679Cexcel\u6210\u529F
whseid.can.not.be.empty=whseid\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8F93\u5165
invoke.success=\u8C03\u7528\u6210\u529F
updata.success=\u66F4\u65B0\u6210\u529F
billnumber.isEmpty=billnumber isEmpty
device.site.code.isEmpty=DEVICE_SITE_CODE isEmpty
calculating.the.number.of.board.scanning.records=\u8BA1\u7B97\u5355\u677F\u626B\u63CF\u8BB0\u5F55\u6761\u6570\uFF1A
the.scheduled.calculation.of.boards.and.batch.material.preparation.and.production.cycle.invoking.are.abnormal=\u5B9A\u65F6\u8BA1\u7B97\u5355\u677F\u53CA\u6279\u6B21\u5907\u6599\u3001\u751F\u4EA7\u5468\u671F\u8C03\u7528\u5F02\u5E38
the.board.and.batch.material.preparation.and.production.cycle.are.being.calculated.and.this.step.is.skipped=WARNNING:\u5355\u677F\u53CA\u6279\u6B21\u5907\u6599\u3001\u751F\u4EA7\u5468\u671F\u8BA1\u7B97\u8FDB\u884C\u4E2D\uFF0C\u672C\u6B21\u6267\u884C\u8DF3\u8FC7
the.635.organization.cannot.generate.the.box.number=\u7EC4\u7EC7635\u65E0\u6CD5\u751F\u6210\u7BB1\u53F7
the.number.of.boxes.cannot.be.less.than.1=\u7BB1\u6570\u4E0D\u80FD\u5C0F\u4E8E1
the.data.in.the.table.is.deleted.successfully.and.the.data.is.inserted.successfully=\u5220\u9664\u8868\u5185\u6570\u636E\u6267\u884C\u6210\u529F\uFF0C\u63D2\u5165\u6570\u636E\u6210\u529F\uFF01
the.service.is.abnormal=\u670D\u52A1\u5F02\u5E38
please.enter.the.material.requisition.form=\u8BF7\u8F93\u5165\u9886\u6599\u5355
the.length.of.the.requisition.order.exceeds.the.upper.limit=\u9886\u6599\u5355\u7684\u957F\u5EA6\u8D85\u51FA\u4E0A\u9650
the.number.of.current.forward.lines.is.larger.than.the.maximum.number.of.data.lines=\u5F53\u524D\u884C\u5927\u4E8E\u6700\u5927\u6570\u636E\u884C\u6570
the.current.page.or.row.number.is.not.a.positive.integer=\u5F53\u524D\u9875\u6216\u884C\u6570\u4E0D\u4E3A\u6B63\u6574\u6570
the.warehouse.is.wrong.and.only.warehouses.*******.***********.and.18.have.board.data=\u4ED3\u5E93\u4E0D\u5BF9\uFF0C\u53EA\u67096\u30017\u30018\u30019\u300110\u300113\u300116\u300117\u300118\u4ED3\u5E93\u624D\u6709\u5355\u677F\u6570\u636E
the.batch.can.contain.only.seven.digits=\u6279\u6B21\u53EA\u80FD\u662F\u4E03\u4F4D\u7684\u6570\u5B57
enter.the.batch.number=\u8BF7\u8F93\u5165\u6279\u6B21\u53F7
please.enter.the.correct.batch.number=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u6279\u6B21\u53F7
the.length.of.the.batch.number.exceeds.100=\u6279\u6B21\u53F7\u7684\u957F\u5EA6\u8D85\u51FA100
task.no.is.not.exists=\u8981\u63D2\u5165\u7684\u4EFB\u52A1\u53F7\u4E0D\u5B58\u5728
info.edit.success=\u4FE1\u606F\u4FEE\u6539\u6210\u529F\u5E76\u4E14\u6DFB\u52A0\u65E5\u5FD7\uFF01
the.excel.is.being.generated.please.notice.the.mail=\u6B63\u5728\u751F\u6210excel\uFF0C\u8BF7\u7559\u610F\u90AE\u4EF6
task.no.is.empty=\u672A\u4F20\u5165\u4EFB\u52A1\u53F7\uFF01
data.insert.successfully=\u6570\u636E\u63D2\u5165\u6210\u529F
data.is.existed=\u6570\u636E\u5DF2\u5B58\u5728\uFF0C\u4E0D\u9700\u8981\u63D2\u5165
bomcode.is.not.empty=\u6599\u5355\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A
bind.success=\u7ED1\u5B9A\u6210\u529F
bind.failed=\u7ED1\u5B9A\u5931\u8D25
insert=\u63D2\u5165:
barcode.data.success=\u6761\u7801\u6570\u636E\u6210\u529F\uFF01
barcode.data.failed=\u6761\u7801\u6570\u636E\u5931\u8D25\uFF01
select.data.err = \u67E5\u8BE2\u4E0D\u5230\u6570\u636E!

sys.get.Null=\u6570\u636E\u5B57\u5178\u67E5\u8BE2\u5F02\u5E38
to.wms.error=\u540C\u6B65WMS\u63A5\u53E3\u4E0D\u6210\u529F{0}
post.infor.error=\u65B0\u589E\u5F02\u5E38INFOR\u4FE1\u606F\u5230\u5907\u4EFD\u8868\u5931\u8D25{0}
save.infor.error=\u4FDD\u5B58\u5F02\u5E38INFOR\u4FE1\u606F\u5230\u5907\u4EFD\u8868\u5931\u8D25{0}
post.his.error=\u65B0\u589E\u5386\u53F2\u8868\u6570\u636E\u5931\u8D25

The.query.result.does.not.meet.the.requirements=\u8F93\u5165\u53C2\u6570\u4E0D\u5408\u6CD5\uFF0C\u8BF7\u68C0\u67E5\u5FC5\u8F93\u53C2\u6570\u662F\u5426\u8F93\u5165\uFF0C\u6279\u91CF\u67E5\u8BE2\u7684\u67E5\u8BE2\u53C2\u6570\u4E2A\u6570\u662F\u5426\u8FC7\u591A
Query.parameters.are.not.set=\u672A\u8BBE\u7F6E\u67E5\u8BE2\u53C2\u6570
input.pagerows=\u8BF7\u8F93\u5165\u5206\u9875\u53C2\u6570\uFF0C\u6BCF\u9875\u4E0D\u5927\u4E8E200\u6761
masgcode.with.entityname=\u4F20\u5165\u751F\u4EA7\u5E03\u70B9\u53C2\u6570\u65F6\u987B\u4F20\u4EFB\u52A1\u53F7
query.condition.empty=\uFFFD\uFFFD\u046F\uFFFD\uFFFD\uFFFD\uFFFD\u03AA\uFFFD\u0563\uFFFD
erp.subinventory.code.empty=ERP\uFFFD\uFFFD\u03AA\uFFFD\u0563\uFFFD
erp.locator.id.empty=ERP\uFFFD\uFFFD\u03BB\u03AA\uFFFD\u0563\uFFFD
transaction.date.empty=\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u03AA\uFFFD\u0563\uFFFD
transaction.type.empty=\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u03AA\uFFFD\u0563\uFFFD
look.up.type.service.error =\u8C03\u7528\u6570\u636E\u5B57\u5178\u670D\u52A1\u9519\u8BEF
more.than.contractNumber.no.or.entityNumber.no=\u8F93\u5165\u7684\u5408\u540C\u53F7\u6216\u4EFB\u52A1\u53F7\u7684\u4E2A\u6570\u6216\u670D\u52A1\u5E76\u53D1\u6570\u5927\u4E8E\u9650\u5236\u6570!
The.number.of.contract.numbers.should.between.zero.and.ten=\u5408\u540C\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E14\u5408\u540C\u53F7\u4E2A\u6570\u4E0D\u80FD\u5927\u4E8E10
The.system.is.abnormal.please.upload.again=\u7CFB\u7EDF\u5F02\u5E38\uFF0C\u6570\u636E\u5168\u90E8\u56DE\u6EDA\uFF0C\u8BF7\u5168\u90E8\u91CD\u65B0\u4E0A\u4F20\uFF01
The.upload.failed.the.boxNum.does.not.exist=\u4E0A\u4F20\u5931\u8D25\uFF0C\u539F\u56E0\u5982\u4E0B\uFF1A\u5355\u636E\u53F7\u4E0D\u5B58\u5728\uFF01
The.upload.failed.check.the.limit.weight=\u672C\u6B21\u4E0A\u4F20\u672A\u6210\u529F\u7BB1\u53F7\u5982\u4E0B\uFF1A\u8BF7\u6838\u5BF9\u88C5\u7BB1\u9650\u91CD\u662F\u5426\u672A\u7EF4\u62A4\u6216\u8BE5\u91CD\u91CF\u503C\u662F\u5426\u5927\u4E8E\u7BB1\u91CD\u4E14\u5C0F\u4E8E\u88C5\u7BB1\u9650\u91CD\u503C\uFF01
No.parameters.were.entered=\u672A\u8F93\u5165\u53C2\u6570
no.data.found=\u6CA1\u6709\u67E5\u8BE2\u5230\u6570\u636E
The.case.number.exceeds.the.allowed.maximum.input.quantity=\u7BB1\u53F7\u8D85\u51FA\u5141\u8BB8\u6700\u5927\u8F93\u5165\u6570\u91CF
input.number.is.not.exists = \u8F93\u5165\u7684\u7BB1\u53F7\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\u540E\u91CD\u65B0\u8F93\u5165\uFF01
input.not.belongs.to.this.taskno=\u8F93\u5165\u7684\u7BB1\u53F7\u4E0D\u5C5E\u4E8E\u5F53\u524D\u7684\u4EFB\u52A1\u53F7\uFF0C\u8BF7\u786E\u8BA4\u540E\u91CD\u65B0\u8F93\u5165\uFF01
this.box.has.been.scaned=\u8BE5\u7BB1\u53F7\u5DF2\u7ECF\u626B\u63CF\u5E76\u5F55\u5165\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4\u540E\u91CD\u65B0\u8F93\u5165\uFF01
the.number.exceed.can.be.scaned.number=\u6808\u677F\u53F7\u7684\u6570\u91CF\u8D85\u51FA\u4E86\u53EF\u626B\u63CF\u6570\u91CF\uFF01
item.info.is.not.exists=\u7BB1\u53F7\u5BF9\u5E94\u7684\u7269\u6599\u4FE1\u606F\u4E0D\u5B58\u5728
pallet.info.is.not.exists=\u6808\u677F\u53F7\u4FE1\u606F\u4E0D\u5B58\u5728
pallet.itemNo.not.same.to.input.itemNo=\u6808\u677F\u53F7\u5BF9\u5E94\u7269\u6599\u548C\u626B\u63CF\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4
is.creating.please.wait=\u5408\u540C\u4EFB\u52A1\u8868\u5F81\u6570\u636E\u6B63\u5728\u751F\u6210\u4E2D\u3002\u8BF7\u7A0D\u5019\u91CD\u8BD5
item.info.not.exists.in.item.list=\u6808\u677F\u53F7\u5BF9\u5E94\u7684\u7269\u6599\u4EE3\u7801\u4E0D\u5728\u7BB1\u53F7\u7684\u7269\u6599\u6E05\u5355\u4E2D
paramNo.were.empty=\u53C2\u6570\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
please.input.one.condition=\u8BF7\u8F93\u5165\u81F3\u5C11\u4E00\u4E2A\u67E5\u8BE2\u6761\u4EF6
get.no.stencil.list.to.be.sychronizationed = \u6CA1\u6709\u9700\u8981\u540C\u6B65\u7684\u94A2\u7F51\u4FE1\u606F
get.item.id.null=\u672A\u67E5\u8BE2\u5230\u7269\u6599\u4EE3\u7801{0}\u7684\u7269\u6599ID

get.warehousd.null=\u5F53\u524D\u5DE5\u5382\u4E0B\u6CA1\u6709\u7EBF\u8FB9\u4ED3

request.param.is.error=\u8BF7\u6C42\u53C2\u6570\u6CA1\u6709\u586B\u597D
request.path.is.null.or.url.is.error=\u8BF7\u6C42\u8DEF\u5F84\u6CA1\u6709\u6216\u9875\u9762\u8DF3\u8F6C\u8DEF\u5F84\u4E0D\u5BF9
find.exception.please.input.bill.id.again=\u53D1\u73B0\u5F02\u5E38\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\u88C5\u7BB1\u5355ID!
bill.id.is.null.or.not.exieit.please.input.apain=\u88C5\u7BB1\u5355ID\u4E3A\u7A7A\u6216\u4E0D\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
billid.billno.is.null.or.not.exieit.please.input.apain=\u88C5\u7BB1\u5355ID\u88C5\u7BB1\u5355NO\u90FD\u4E3A\u7A7A\u6216\u4E0D\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
from.bill.id.can.not.find.box.info=\u88C5\u7BB1\u5355ID,\u5BF9\u5E94\u7684\u88C5\u7BB1\u5355\u4FE1\u606F\u4E0D\u5B58\u5728!
success.get.box.info=\u6210\u529F\u83B7\u5F97\u88C5\u7BB1\u5355\u4FE1\u606F\uFF01
Each.input.cannot.exceed.three.contract.numbers=\u6BCF\u4E00\u6B21\u8F93\u5165\u4E0D\u80FD\u8D85\u8FC7\u4E09\u4E2A\u4E2A\u5408\u540C\u53F7
Each.input.cannot.exceed.hundred.box.numbers=\u6BCF\u4E00\u6B21\u8F93\u5165\u4E0D\u80FD\u8D85\u8FC7100\u4E2A\u88C5\u7BB1\u5355\u53F7
tasklist.is.null=\u4EA4\u6613\u660E\u7EC6\u4E3A\u7A7A
update.flow.status.error= \u66F4\u65B0\u4EA4\u6613\u6807\u5FD7\u5931\u8D25
sys.not.set=\u6570\u636E\u5B57\u5178:{0}\u672A\u914D\u7F6E

find.fail.because.input.subbarcode.is.null=\u67E5\u8BE2\u5931\u8D25\uFF0C\u56E0\u4E3A\u8F93\u5165\u7684\u5B50\u6761\u7801\u6570\u636E\u4E3A\u7A7A
find.fail.because.input.subbarcode.is.more.than.200=\u67E5\u8BE2\u5931\u8D25\uFF0C\u56E0\u4E3A\u8F93\u5165\u7684\u5B50\u6761\u7801\u6570\u636E\u4E2A\u6570\u8D85\u8FC7200
data.success.find=\u67E5\u8BE2\u6210\u529F
data.find.operation.is.ok.but.not.find.data=\u67E5\u8BE2\u5B8C\u6210\uFF0C\u4F46\u662F\u672A\u627E\u5230\u76F8\u5173\u6570\u636E
find.fail.because.input.data.type.is.not.C=\u67E5\u8BE2\u5931\u8D25\uFF0C\u56E0\u4E3A\u8F93\u5165\u6570\u636E\u7C7B\u578B\u4E0D\u662F\u5B57\u7B26C
find.fail.because.input.data.should.be.english =\u67E5\u8BE2\u5931\u8D25\uFF0C\u8F93\u5165\u5206\u5272\u7B26\u5305\u542B\u4E2D\u6587\u9017\u53F7
no.more.than.200.barcode.of.production.material.can.be.input.each.time=\u6BCF\u4E00\u6B21\u8F93\u5165\u7684\u751F\u4EA7\u7269\u6599\u6761\u7801\u4E0D\u80FD\u8D85\u8FC7200\u4E2A
input.entityname.is.null.please.check.entityname=\u8F93\u5165\u7684\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.palltedesc.is.null.please.check.palltedesc=\u8F93\u5165\u7684\u6258\u76D8\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.productdesc.is.null.please.check.productdesc=\u8F93\u5165\u7684\u4EA7\u54C1\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.boxtype.is.null.please.check.boxtype=\u8F93\u5165\u7684\u7BB1\u578B\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.stacknum.is.null.please.check.stacknum=\u8F93\u5165\u7684\u6258\u76D8\u5806\u53E0\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.createby.is.null.please.check.createby=\u8F93\u5165\u7684\u521B\u5EFA\u4EBA\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
input.organizationid.is.null.please.check.organizationid=\u8F93\u5165\u7684\u7EC4\u7EC7ID\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
billno.and.palletno.input.only.one=\u7BB1\u53F7\u548C\u6258\u76D8\u53F7\u53EA\u80FD\u8F93\u5165\u4E00\u4E2A
pallet.exists.no.scan.end.bill=\u6258\u76D8{0}\u5B58\u5728\u672A\u626B\u63CF\u7ED3\u675F\u7684\u7BB1{1}\uFF0C\u65E0\u6CD5\u83B7\u53D6\u6258\u76D8\u660E\u7EC6\u6570\u636E
billno.no.pallet.no.get.bill.detail.info=\u7BB1:{0}\u672A\u88C5\u6258\u76D8\uFF0C\u65E0\u6CD5\u83B7\u53D6\u6258\u76D8\u660E\u7EC6\u6570\u636E
input.palltedesc.number.is.over.two.hundred=\u8F93\u5165\u7684\u6258\u76D8\u63CF\u8FF0\u4E0D\u80FD\u8D85\u8FC7200\u5B57\uFF0C\u8BF7\u68C0\u67E5
input.entityname.not.find.please.check.entityname=\u8F93\u5165\u7684\u4EFB\u52A1\u53F7\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5
boxtype.not.find.width.and.length.please.check=\u8F93\u5165\u7684\u7BB1\u578B\u4EE3\u7801\u67E5\u8BE2\u5BF9\u5E94\u7684\u957F\u3001\u5BBD\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\u3002
palletid.not.find.please.check=\u53D1\u751F\u5F02\u5E38\uFF0C\u6258\u76D8ID\u4E0D\u5B58\u5728\uFF0C\u8BF7\u8054\u7CFB\u76F8\u5173\u8D1F\u8D23\u4EBA\u5458
palletno.not.find.please.check=\u53D1\u751F\u5F02\u5E38\uFF0C\u6258\u76D8\u7F16\u53F7\u4E0D\u5B58\u5728\uFF0C\u8BF7\u8054\u7CFB\u76F8\u5173\u8D1F\u8D23\u4EBA\u5458
save.pallet.error.please.check=\u53D1\u751F\u5F02\u5E38\uFF0C\u6258\u76D8\u4FE1\u606F\u7EF4\u62A4\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u76F8\u5173\u8D1F\u8D23\u4EBA\u5458
save.pallet.seccess=\u6258\u76D8\u4FE1\u606F\u7EF4\u62A4\u6210\u529F
input.createby.not.eight.please.cheack=\u8F93\u5165\u7684\u521B\u5EFA\u4EBA\u4E0D\u662F14\u4F4D\u5DE5\u53F7\u7684\u540E8\u4F4D\uFF0C\u8BF7\u68C0\u67E5
unknown.error.handler=\u7CFB\u7EDF\u51FA\u73B0\u672A\u77E5\u9519\u8BEF\uFF0C\u9519\u8BEF\u65E5\u5FD7\u7F16\u53F7\u4E3A\uFF1A{0},\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u4EBA\u5458\u5904\u7406\uFF0C\u8C22\u8C22\uFF01

operation.success=\u64CD\u4F5C\u6210\u529F
entityname.or.materialcode.at.least.one=\u64CD\u4F5C\u5931\u8D25\uFF0C\u8F93\u5165\u4EFB\u52A1\u53F7\u6216\u8005\u7269\u6599\u4EE3\u7801\u81F3\u5C11\u4E00\u4E2A\uFF0C\u8BF7\u786E\u8BA4
barcodes.can.not.gt.50=\u6761\u7801\u4E0D\u80FD\u5927\u4E8E50\u4E2A
input.param.can.not.find.data=\u64CD\u4F5C\u5931\u8D25\uFF0C\u8F93\u5165\u6570\u636E\u672A\u627E\u5230\u76F8\u5173\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
can.not.find.entity.or.site.infor=\u64CD\u4F5C\u5931\u8D25\uFF0C\u672A\u627E\u5230\u4EFB\u52A1\u6216\u7AD9\u70B9\u76F8\u5173\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4

input.productdesc.not.find.please.check.productdesc=\u64CD\u4F5C\u5931\u8D25\uFF0C\u8F93\u5165\u7684\u4EA7\u54C1\u63CF\u8FF0\u6821\u9A8C\u4E0D\u901A\u8FC7
pallet.information.is.no=\u65E0\u6258\u76D8\u7EF4\u62A4\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
pallet.information.is.not.scaned=\u6258\u76D8\u672A\u626B\u63CF\u7ED3\u675F\uFF0C\u8BF7\u786E\u8BA4
the.height.of.pallet.must.be.more.than.zero=\u6258\u76D8\u7801\u579B\u9AD8\u5EA6\u5FC5\u987B\u662F\u5927\u4E8E\u96F6\u7684\u6570\u5B57
the.height.of.pallet.is.no.more.than.three.thousand=\u6258\u76D8\u7801\u579B\u9AD8\u5EA6\u4E0D\u80FD\u8D85\u8FC73000.00
only.two.decimal.places.are.allowed.for.palletizing.height=\u6258\u76D8\u7801\u579B\u9AD8\u5EA6\u53EA\u5141\u8BB8\u4FDD\u7559\u4E24\u4F4D\u5C0F\u6570
the.total.weight.of.pallet.must.be.more.than.zero=\u6258\u76D8\u603B\u91CD\u91CF\u5FC5\u987B\u662F\u5927\u4E8E\u96F6\u6570\u503C
The.maximum.total.weight.of.pallet.cannot.exceed.9999.9=\u6258\u76D8\u603B\u91CD\u91CF\u6700\u5927\u503C\u4E0D\u80FD\u8D85\u8FC79999.9
only.one.decimal.place.is.allowed.for.the.total.weight.of.pallet=\u6258\u76D8\u603B\u91CD\u91CF\u53EA\u5141\u8BB8\u5E26\u4E00\u4F4D\u5C0F\u6570
pallet.and.height.and.weight.and.lastUpdateby.are.not.empty=\u6258\u76D8\u53F7\u3001\u9AD8\u5EA6\u3001\u91CD\u91CF\u548C\u6700\u540E\u66F4\u65B0\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
pallet.is=\u6258\u76D8\u603B\u91CD\u91CF\u7EF4\u62A4\u6709\u8BEF,\uFF0C\u8BF7\u68C0\u67E5\u9876\u5C42\u7BB1\u5B9A\u4E49\u4E2D\u6258\u76D8\u7BB1\u578B\u4EE3\u7801\u5BF9\u5E94\u7684\u7BB1\u91CD\u53CA\u626B\u63CF\u7684\u6BCF\u7BB1\u91CD\u91CF\u662F\u5426\u6B63\u786E\uFF01
job.number.does.not.exist.please.re-enter=\u5DE5\u53F7\u4E0D\u5B58\u5728\u8BF7\u91CD\u65B0\u8F93\u5165

billno.already.bind.palletno=\u7BB1\u53F7\u5DF2\u7ECF\u7ED1\u5B9A\u6258\u76D8
not.find.pallet.info=\u6CA1\u6709\u6258\u76D8\u4FE1\u606F\uFF0C\u4E0D\u80FD\u8FDB\u884C\u88C5\u7BB1\u5355\u626B\u63CF\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
failed.to.save.scan.info=\u53D1\u751F\u5F02\u5E38\uFF0C\u4FDD\u5B58\u626B\u63CF\u4FE1\u606F\u5931\u8D25
failed.to.update.scan.info=\u53D1\u751F\u5F02\u5E38\uFF0C\u6258\u76D8\u4FE1\u606F\u66F4\u65B0\u5931\u8D25
pallet.mfg.site.address.different=\u6309\u7AD9\u578B\u4EA4\u4ED8\u6A21\u5F0F\u4E0B\uFF0C\u540C\u4E00\u6258\u76D8\u4E0A\u7684\u7BB1\u53F7\u7AD9\u70B9\u5730\u5740\u5E94\u8BE5\u76F8\u540C\uFF0C\u8BF7\u68C0\u67E5\uFF01
validate.number.err=\u6258\u76D8\u53F7{0}\u5DF2\u626B\u63CF\u6570\u91CF\u5FC5\u987B\u7B49\u4E8E\u9700\u626B\u63CF\u6570\u91CF\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF\u7ED3\u675F
box.weight.is.zero=\u7BB1\u91CD\u91CF\u4E0D\u80FD\u4E3A\u7A7A\u6216\u8005\u4E3A0\uFF0C\u8BF7\u7EF4\u62A4\uFF0C\u8C22\u8C22\uFF01
box.and.pallet.entityname.is.diff=\u88C5\u7BB1\u5355\u53F7\u5BF9\u5E94\u7684\u4EFB\u52A1\u53F7\uFF0C\u5FC5\u987B\u4E0E\u6258\u76D8\u53F7\u6240\u5BF9\u5E94\u8BE5\u7684\u4EFB\u52A1\u53F7\u76F8\u5339\u914D
not.find.pallet.info.please.check=\u6CA1\u6709\u6258\u76D8\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4\u6258\u76D8\u53F7\u662F\u5426\u6B63\u786E
fill.empty.box.bill.count.err=\u6258\u76D8\u53F7{0}\u7684\u6258\u76D8\u586B\u5145\u7A7A\u7BB1\u6570\u91CF\u4E3A{1}\uFF0C\u5DF2\u7ECF\u626B\u63CF\u4E86{2}\u4E2A\u7A7A\u7BB1\u5355\uFF0C\u4E0D\u5141\u8BB8\u518D\u626B\u63CF\uFF0C\u8BF7\u68C0\u67E5
stack.count.err=\u6258\u76D8\u53F7{0}\u7684\u6258\u76D8\u5806\u53E0\u6570\u91CF\u4E3A{1}\uFF0C\u5DF2\u7ECF\u626B\u63CF\u4E86{2}\u4E2A\u88C5\u7BB1\u5355\uFF0C\u4E0D\u5141\u8BB8\u518D\u626B\u63CF\uFF0C\u8BF7\u68C0\u67E5
pallet.end.flag.is.yes=\u5F53\u524D\u6258\u76D8\u53F7\u7684\u201C\u662F\u5426\u626B\u63CF\u7ED3\u675F\u201D\u4E3A\u201C\u662F\u201D\uFF0C\u4E0D\u5141\u8BB8\u8FDB\u884C\u88C5\u7BB1\u5355\u626B\u63CF
pallet.had.already.scaned=\u8F93\u5165\u7684\u88C5\u7BB1\u5355\u53F7\u5DF2\u7ECF\u626B\u63CF\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u626B\u63CF
pallet.had.been.scan.in.current=\u8BE5\u88C5\u7BB1\u5355\u53F7{0}\u5DF2\u7ECF\u626B\u63CF\u81F3\u6258\u76D8\u53F7{1}\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u626B\u63CF
real.box.count.err=\u6258\u76D8\u53F7{0}\u7684\u6258\u76D8\u6B63\u5E38\u7BB1\u53F7\u6570\u91CF\u4E3A{1}\uFF0C\u5DF2\u7ECF\u626B\u63CF\u4E86{2}\u4E2A\u6B63\u5E38\u7BB1\u53F7\u5355\uFF0C\u4E0D\u5141\u8BB8\u518D\u626B\u63CF\uFF0C\u8BF7\u68C0\u67E5
box.bill.count.err=\u6258\u76D8\u53F7{0}\u7684\u6258\u76D8\u5806\u53E0\u6570\u91CF\u4E3A{1}\uFF0C\u5DF2\u7ECF\u626B\u63CF\u4E86{2}\u4E2A\u88C5\u7BB1\u5355\uFF0C\u4E0D\u5141\u8BB8\u518D\u626B\u63CF\uFF0C\u8BF7\u68C0\u67E5
pallet.number.not.exists=\u88C5\u7BB1\u5355\u53F7\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u626B\u63CF
pallet.site.diff.with.box.bill.site=\u6258\u76D8\u7AD9\u70B9\u4E0E\u88C5\u7BB1\u5355\u53F7\u7AD9\u70B9\u4E0D\u4E00\u81F4\uFF0C\u4E0D\u5141\u8BB8\u626B\u63CF
bill.box.is.null=\u8F93\u5165\u7684\u7BB1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
pallet.is.empty=\u8F93\u5165\u7684\u6258\u76D8\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
oranization.is.empty=\u8F93\u5165\u7684\u7EC4\u7EC7ID\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
scanby.is.empty=\u8F93\u5165\u7684\u626B\u63CF\u4EBA\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
scanby.is.empty.gt20=\u8F93\u5165\u7684\u626B\u63CF\u4EBA\u4E0D\u80FD\u4E3A\u7A7A\u6216\u5927\u4E8E20\uFF0C\u8BF7\u68C0\u67E5
scandate.is.empty=\u8F93\u5165\u7684\u626B\u63CF\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
position.is.empty=\u8F93\u5165\u7684\u4F4D\u7F6E\u7801\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
entry.bill.detailids.not.null=\u8F93\u5165\u7684\u5165\u5E93\u5355\u660E\u7EC6\u53F7\u4E0D\u80FD\u4E3A\u7A7A\u6216\u5927\u4E8E1000\uFF0C\u8BF7\u68C0\u67E5
update.rows.is.zero=\u66F4\u65B0\u7684\u884C\u6570\u4E3A0
entry.bill.status.not.null=\u8F93\u5165\u7684\u5165\u5E93\u5355\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A\u6216\u5927\u4E8E10\uFF0C\u8BF7\u68C0\u67E5
entry.bill.return.reason.gt200=\u8F93\u5165\u7684\u5165\u5E93\u5355\u56DE\u9000\u539F\u56E0\u4E0D\u80FD\u5927\u4E8E200\uFF0C\u8BF7\u7B80\u5355
entry.bill.postby.gt20=\u8F93\u5165\u7684\u5165\u5E93\u5355\u8BB0\u8D26\u4EBA\u4E0D\u80FD\u5927\u4E8E20\uFF0C\u8BF7\u68C0\u67E5
entry.bill.submitby.gt20=\u8F93\u5165\u7684\u5165\u5E93\u5355\u63D0\u4EA4\u4EBA\u4E0D\u80FD\u5927\u4E8E20\uFF0C\u8BF7\u68C0\u67E5
entry.bill.receiveby.gt20=\u8F93\u5165\u7684\u5165\u5E93\u5355\u63A5\u6536\u4EBA\u4E0D\u80FD\u5927\u4E8E20\uFF0C\u8BF7\u68C0\u67E5
entry.bill.returnby.gt20=\u8F93\u5165\u7684\u5165\u5E93\u5355\u56DE\u9000\u4EBA\u4E0D\u80FD\u5927\u4E8E20\uFF0C\u8BF7\u68C0\u67E5
entry.bill.not.null=\u8F93\u5165\u7684\u5165\u5E93\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
stockno.not.null=\u8F93\u5165\u7684\u5B50\u5E93\u5B58\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
billno.or.palletno.not.null=\u8F93\u5165\u7684\u7BB1\u53F7\u6216\u6258\u76D8\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u68C0\u67E5
scanby.is.err=\u8F93\u5165\u7684\u626B\u63CF\u4EBA\u4E0D\u80FD\u67E5\u5230\u5BF9\u5E94\u7684\u7528\u6237ID\uFF0C\u8BF7\u68C0\u67E5
pallet.id.is.err=\u53D1\u751F\u5F02\u5E38\uFF0C\u6258\u76D8ID\u83B7\u53D6\u5931\u8D25
wis.id.list.is.empty.or.exceed.max=wisidList\u4E3A\u7A7A\u6216\u8D85\u8FC7\u6700\u5927\u6570\u91CF: {0}
sku.list.is.empty.or.exceed.max=skuList\u4E3A\u7A7A\u6216\u8D85\u8FC7\u6700\u5927\u6570\u91CF: {0}
wis.id.can.only.contains.number.letter.underline=wisId\u53EA\u80FD\u5305\u542B\u6570\u5B57\u5B57\u6BCD\u4E0B\u5212\u7EBF

contract.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u5408\u540C\u53F7\u4E3A\u7A7A
entity.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u4EFB\u52A1\u53F7\u4E3A\u7A7A
barcode.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u7269\u6599\u6761\u7801\u4E3A\u7A7A
itemcode.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u7269\u6599\u4EE3\u7801\u4E3A\u7A7A
boxno.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u7BB1\u53F7\u4E3A\u7A7A
updateman.is.null=DQAS\u7CFB\u7EDF\u626B\u63CF\u6821\u9A8C\u5931\u8D25,\u64CD\u4F5C\u4EBA\u4E3A\u7A7A
input.param.is.batch=\u8F93\u5165\u53C2\u6570\u5305\u542B\u9017\u53F7\uFF0C\u4E0D\u652F\u6301\u6279\u91CF\u8F93\u5165\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165

Job.number.does.not.exist=\u5DE5\u53F7\u4E0D\u5B58\u5728
any.thing.no=\u6BCF\u4E00\u4E2A\u53C2\u6570\u90FD\u4E0D\u80FD\u4E3A\u7A7A
task.no=\u4EFB\u52A1\u53F7\u4E0D\u5B58\u5728
no.site=\u4EFB\u52A1\u53F7\u4E0B\u6CA1\u6709\u7AD9\u70B9
there.are.no.materials.configured.for.this.site=\u6B64\u7AD9\u70B9\u6CA1\u6709\u914D\u7F6E\u7269\u6599\u4FE1\u606F
task.number.does.not.match.with.environmental.attributes=\u6B64\u7269\u6599\u6761\u7801\u7684\u73AF\u4FDD\u5C5E\u6027\u4E0E\u4EFB\u52A1\u53F7\u7684\u73AF\u4FDD\u5C5E\u6027\u4E0D\u76F8\u7B26\u5408
barcode.information.is.not.found=\u6761\u7801\u4FE1\u606F\u67E5\u4E0D\u5230
the.barcode.has.been.bound=\u8BE5\u6761\u7801\u5DF2\u7ECF\u5728\u5176\u4ED6\u7AD9\u70B9\u6216\u8005\u4EFB\u52A1\u53F7\u7ED1\u5B9A\uFF0C\u82E5\u8981\u7ED1\u5B9A\uFF0C\u8BF7\u89E3\u7ED1\u8BE5\u6761\u7801
The.barcode.under.this.configuration.has.been.scanned=\u8BE5\u914D\u7F6E\u4E0B\u7684\u6761\u7801\u5DF2\u7ECF\u626B\u5B8C
Barcode.under.configuration.modification.has.been.scanned=\u8BE5\u914D\u7F6E\u4E0B\u7684\u6761\u7801\u5DF2\u7ECF\u626B\u63CF\u7ED3\u675F
The.number.of.scanned.bar.code.is.greater.than.the.number.of.configured.bar.code=\u8BE5\u6761\u7801\u626B\u63CF\u6570\u5927\u4E8E\u914D\u7F6E\u6570
The.main.bar.code.is.controlled.by.technical.transformation=\u8BE5\u6761\u7801\u53D7\u6280\u6539\u63A7\u5236
Insert.main.bar.code.to.report.error=\u63D2\u5165\u4E3B\u6761\u7801\u62A5\u9519
Operation.successful=\u64CD\u4F5C\u6210\u529F
No.corresponding.configuration.information.was.found.for.barcode=\u6761\u7801\u6CA1\u6709\u5BF9\u5E94\u7684\u914D\u7F6E\u7269\u6599\u4FE1\u606F
dqas.verification.failed=DQAS\u6821\u9A8C\u4E0D\u901A\u8FC7

many.mateial.Information=\u7269\u6599\u4EE3\u7801\u5339\u914D\u5230\u591A\u4E2A\u914D\u7F6E\u7269\u6599\u4FE1\u606F

no.find.box.info=\u672A\u67E5\u8BE2\u5230\u88C5\u7BB1\u4FE1\u606F\uFF0C\u8BF7\u786E\u8BA4
no.bom.information.from.boxinfo=\u672A\u627E\u5230\u7BB1\u5BF9\u5E94\u7684BOQ\u4FE1\u606F\uFF0C\u65E0\u6CD5\u6253\u5370
template.info.is.null=\u6A21\u677F\u8BED\u8A00\u4E0D\u80FD\u4E3A\u7A7A
bom.not.loaded=\u8BE5\u7BB1\u672A\u5B8C\u6210\u88C5\u7BB1\u626B\u63CF\uFF0C\u4E0D\u5141\u8BB8\u6253\u5370
box.is.virture=\u6B64\u7BB1\u4E3A\u865A\u62DF\u7BB1\uFF0C\u65E0\u6CD5\u6253\u5370\uFF0C\u8BF7\u786E\u8BA4
no.cbom.information.from.boxinfo=\u672A\u627E\u5230\u7BB1\u5BF9\u5E94\u7684CBOM\u4FE1\u606F\uFF0C\u65E0\u6CD5\u6253\u5370
stock.no.is.null=\u5728\u9014\u5E93\u5B58\u4E0D\u80FD\u4E3A\u7A7A
warehouseId.is.null=\u7EBF\u8FB9\u4ED3ID\u4E0D\u80FD\u4E3A\u7A7A
get.flow.qty.error=\u83B7\u53D6\u4ED3\u50A8\u4EA4\u6613\u4FE1\u606F\u5931\u8D25


num.must.over.zero=\u8F93\u5165\u6BCF\u9875\u5C55\u793A\u6570\u91CF\u548C\u9875\u7801\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0\u7684\u6574\u6570
page.is.null=\u8F93\u5165\u7684\u67E5\u8BE2\u9875\u6570\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
rows.is.null=\u8F93\u5165\u7684\u67E5\u8BE2\u6BCF\u9875\u7684\u6570\u91CF\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
rows.is.over.200=\u67E5\u8BE2\u5931\u8D25\uFF0C\u8F93\u5165\u7684\u67E5\u8BE2\u6BCF\u9875\u7684\u6570\u91CF\u6700\u5927\u4E3A200\uFF0C\u8BF7\u786E\u8BA4
item.code.num.is.over.50=\u7269\u6599\u4EE3\u7801\u6279\u91CF\u67E5\u8BE2\u6570\u91CF\u6700\u5927\u4E3A50\uFF0C\u8BF7\u786E\u8BA4
input.data.contain.point=\u8F93\u5165\u6BCF\u9875\u5C55\u793A\u6570\u91CF\u548C\u9875\u7801\u6570\u91CF\u5FC5\u987B\u662F\u6574\u6570\uFF0C\u4E0D\u80FD\u5C0F\u6570

Each.parameter.must.be.entered=\u6BCF\u4E2A\u53C2\u6570\u5FC5\u987B\u8F93\u5165
Softtaskno.startimeendtime.atleastone=\u8F6F\u4EF6\u4EFB\u52A1\u53F7(\u5F00\u59CB\u65F6\u95F4\u7ED3\u675F\u65F6\u95F4)\u81F3\u5C11\u8F93\u5165\u4E00\u4E2A\uFF0C\u5176\u5B83\u5FC5\u8F93
Per.org.cannot.repeat.in.8000220=\u6709\u7EC4\u7EC7\u5B58\u5728\u591A\u4E2A\u8F6F\u4EF6\u5B50\u5E93\u5B58\uFF0C\u8BF7\u91CD\u65B0\u7EF4\u62A4\u6570\u636E\u5B57\u5178\u7F16\u53F7\uFF1A8000220
Paging.parameter.cannot.be.greater.than.200=\u5206\u9875\u53C2\u6570\u4E0D\u80FD\u5927\u4E8E200
The.number.of.entries.per.page.cannot.be.less.than.or.equalled.to.0=\u6BCF\u9875\u7684\u884C\u6570\u4E0D\u80FD\u5C0F\u4E8E\u6216\u8005\u7B49\u4E8E0
The.requested.page.number.cannot.be.less.than.or.equalled.to.0=\u8BF7\u6C42\u7684\u9875\u7801\u4E0D\u80FD\u5C0F\u4E8E\u6216\u8005\u7B49\u4E8E0
The.start.time.of.query.cannot.be.greater.than.the.end.time=\u67E5\u8BE2\u7684\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u5927\u4E8E\u7ED3\u675F\u65F6\u95F4
TASK.SOFT.NO.LESS.100=\u8F6F\u4EF6\u4EFB\u52A1\u53F7\u6570\u636E\u5FC5\u987B\u5C0F\u4E8E\u7B49\u4E8E100
There.is.no.data.for.pushing.PDM.during.this.period.or.the.push.fails=\u63A8\u9001PDM\u8FD9\u6BB5\u65F6\u95F4\u5185\uFF0C\u6CA1\u6709\u6570\u636E\u6216\u8005\u63A8\u9001\u5931\u8D25
Box.list.quantity.is.too.large=\u7BB1\u5217\u8868\u6570\u91CF\u8FC7\u5927



timer.synchronize.fail=informatica-CMS-WMES-cdm_contract_headers synchronization stop
timer.synchronize.wmes.fail=informatica-CMS-WMES-cdm_contract_lines synchronization stop
timer.synchronize.fail.head=informatica-app-mes-cdm_contract_headers synchronization stop
timer.synchronize.fail.line=informatica-app-mes-cdm_contract_lines synchronization stop
Organization.Id.must.be.entered=\u7EC4\u7EC7id\u5FC5\u987B\u8F93\u5165
Sub.bar.codes.of=\u53D7\u6280\u6539\u7BA1\u63A7\u7684\u5B50\u6761\u7801\u4E3A:
main.barcode.of=\u53D7\u6280\u6539\u7BA1\u63A7\u7684\u4E3B\u6761\u7801\u4E3A:
Material.barcode.must.be.entered=\u6761\u7801\u5FC5\u987B\u8F93\u5165
The.function.module.must.be.entered=\u529F\u80FD\u6A21\u5757\u5FC5\u987B\u8F93\u5165
The.page.is.too.big=\u6BCF\u9875\u6570\u91CF\u592A\u5927
query.param.is.empty=\u67E5\u8BE2\u65F6\u95F4\u8D77\u59CB\u65F6\u95F4\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u8D85\u8FC715\u5929.
pallet.number.is.over.50=\u6258\u76D8\u8F93\u5165\u7684\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC750
query.task.list.is.empty=\u8F93\u5165\u7684\u4EFB\u52A1\u53F7\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
query.task.is.no.more.than.200=\u8F93\u5165\u7684\u4EFB\u52A1\u5217\u8868\u4E0D\u80FD\u8D85\u8FC7200\u4E2A
entity.name.not.null=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF01
boxup.is.not.null.or.gt.set.upper.limit=\u7BB1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\u6216\u5927\u4E8E\u8BBE\u7F6E\u7684\u4E0A\u9650
SN.is.null.or.exceeds.the.set.upper.limit=SN\u4E0D\u80FD\u4E3A\u7A7A\u6216\u5927\u4E8E\u8BBE\u7F6E\u7684\u4E0A\u9650
auto.flag.is.not.null=\u81EA\u52A8\u4E0A\u4F20\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A
boxup.at.least.one.is.exists=\u8F93\u5165\u7684\u7BB1\u53F7\u81F3\u5C11\u6709\u4E00\u4E2A\u4E0D\u5B58\u5728
entity.name.list.length.should.less.50=\u4EFB\u52A1\u53F7\u6700\u591A\u53EA\u80FD\u8F93\u516550\u4E2A\uFF0C\u8BF7\u786E\u8BA4\uFF01
current.page.not.null=\u8F93\u5165\u591A\u4E2A\u4EFB\u52A1\u53F7\u65F6\uFF0C\u9700\u8F93\u5165\u5F53\u524D\u9875\u8FDB\u884C\u67E5\u8BE2\uFF01
page.size.not.greater.than.200=\u8F93\u5165\u591A\u4E2A\u4EFB\u52A1\u53F7\u65F6\uFF0C\u6BCF\u4E00\u9875\u7684\u9875\u6570\u4E0D\u80FD\u5927\u4E8E200
validate.input.type.of.check.binding.error=\u8F93\u5165\u7C7B\u578B\u662F\u4E3A\u7A7A\u6216\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u8BF7\u786E\u8BA4\uFF01
params.list.is.null=\u53C2\u6570\u5217\u8868\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4\uFF01
size.out.bound.params.list.of.check.binding=\u53C2\u6570\u5217\u8868\u4E2A\u6570\u8D85\u8FC750,\u8BF7\u786E\u8BA4\uFF01
entity.name.not.all.exist=\u5B58\u5728\u4EFB\u52A1\u53F7{0}\u4E0D\u5728\u7CFB\u7EDF\u4E2D\uFF0C\u8BF7\u786E\u8BA4\uFF01
exist.not.bound.entity.name=\u5B58\u5728\u5FC5\u7ED1\u7269\u6599\u672A\u7ED1\u7684\u4EFB\u52A1\u53F7\uFF0C\u8BF7\u786E\u8BA4\uFF01
exist.not.bound.mfg.site.id=\u5B58\u5728\u5FC5\u7ED1\u7269\u6599\u672A\u7ED1\u7684\u751F\u4EA7\u5E03\u70B9ID\uFF0C\u8BF7\u786E\u8BA4\uFF01
The.barcode.is.larger.than.five.layers.and.cannot.be.bound=\u6761\u7801\u5927\u4E8E\u4E94\u5C42\uFF0C\u4E0D\u80FD\u7ED1\u5B9A
Same.code.same.material.code.with.substitution.relationship=\u76F8\u540C\u4EE3\u7801/\u6709\u66FF\u4EE3\u5173\u7CFB\u7684\u76F8\u540C\u7269\u6599\u4EE3\u7801(
The.cannot.be.bound.hierarchically.as.a.primary.sub.relationship=)\u7684\u4E0D\u80FD\u505A\u4E3B\u5B50\u5173\u7CFB\u5206\u5C42\u6B21\u7ED1\u5B9A
This.barcode.has.been.bound.to=\u8BE5\u6761\u7801\u5DF2\u7ECF\u7ED1\u5B9A\u5230
No.binding.is.allowed.on.the.barcode=\u6761\u7801\u4E0A\uFF0C\u4E0D\u5141\u8BB8\u518D\u7ED1\u5B9A
entityName=\u4EFB\u52A1
This.barcode.is.a.primary.barcode.and.cannot.be.bound.to.its.own.sub.barcode=\u8BE5\u6761\u7801\u662F\u4E3B\u6761\u7801\u4E0D\u80FD\u7ED1\u5B9A\u5230\u81EA\u5DF1\u7684\u5B50\u6761\u7801
If.this.subassembly.is.bound.to.this.parent.subassembly.the.binding.level.will.be.greater.than.5=\u8BE5\u5B50\u90E8\u4EF6\u82E5\u7ED1\u5B9A\u8BE5\u7236\u90E8\u4EF6\uFF0C\u5219\u7ED1\u5B9A\u5C42\u6B21\u4F1A\u5927\u4E8E5\u5C42
Primary.barcode.and.sub.barcode.cannot.be.the.same.barcode=\u4E3B\u6761\u7801\u548C\u5B50\u6761\u7801\u4E0D\u80FD\u662F\u540C\u4E00\u6761\u7801
The.barcode.is.bound.to.the.task.number=\u6761\u7801\u7ED1\u5B9A\u5728\u4EFB\u52A1\u53F7(
Next.to.bind.please.unbind.the.barcode=)\u4E0B\uFF0C\u82E5\u8981\u7ED1\u5B9A\uFF0C\u8BF7\u89E3\u7ED1\u8BE5\u6761\u7801
Verification.succeeded=\u6821\u9A8C\u6210\u529F
This.barcode.has.been.bound.in.other.sites.To.bind.please.unbind.the.barcode=\u8BE5\u6761\u7801\u5DF2\u7ECF\u5728\u5176\u4ED6\u7AD9\u70B9\u7ED1\u5B9A\uFF0C\u82E5\u8981\u7ED1\u5B9A\uFF0C\u8BF7\u89E3\u7ED1\u8BE5\u6761\u7801
The.barcode.has.been.scanned=\u8BE5\u6761\u7801\u5DF2\u626B\u5B8C
this.barcode.dt.barcode=\u8BE5\u6761\u7801
itemCode.dt.itemCode=\u7684\u7269\u6599\u4EE3\u7801
messageID.dt.messageID=\u7269\u6599id
repleacedID.dt.repleacedID=\u66FF\u4EE3\u7269\u6599\u4EE3\u7801\u4E0D\u5B58\u5728
diction.is.null=\u6570\u636E\u5B57\u5178\u914D\u7F6E\u4E3A\u7A7A
scan.no.end={0}\u672A\u626B\u63CF\u7ED3\u675F\uFF0C\u65E0\u6CD5\u83B7\u53D6\u7BB1\u660E\u7EC6\u6570\u636E
bill.and.orgid.cannot.empty=\u88C5\u7BB1\u5355\u53F7\u548C\u7EC4\u7EC7\u4E0D\u80FD\u4E3A\u7A7A
no.bill.info=\u6CA1\u6709\u88C5\u7BB1\u4FE1\u606F
token.is.not.null=token\u503C\u9A8C\u8BC1\u5931\u8D25 
sessionid.is.not.null=sessionid\u4E0D\u80FD\u4E3A\u7A7A
no.taskname=\u6CA1\u6709\u4EFB\u52A1\u53F7\uFF08\u5BFC\u5165\u4EFB\u52A1&&\u672A\u626B\u63CF\u4EFB\u52A1&&BOQ\u4EFB\u52A1\uFF09
Abnormal.verification.of.job.number=\u5DE5\u53F7\u9A8C\u8BC1\u5931\u8D25
The.maximum.value.of.the.box.list.cannot.exceed=\u7BB1\u5217\u8868\u7684\u6700\u5927\u503C:
The.page.number.is.no.more.then=\u9875\u7801\u4E0D\u80FD\u8D85\u8FC7
The.page.or.The.CurrentPage.is.no.negative=\u9875\u7801\u3001\u5F53\u524D\u9875\u5FC5\u987B\u5927\u4E8E0
the.param.is.empty=\u53C2\u6570\u4E3A\u7A7A
the.box.is.empty=\u6258\u76D8\u548C\u7BB1\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
the.box.is.not.empty=\u6258\u76D8\u548C\u7BB1\u4E0D\u80FD\u540C\u65F6\u8F93\u5165
the.entity.is.not.exist=\u4EFB\u52A1\u53F7\u4E0D\u5B58\u5728
the.SubInvCode.is.not.exist=\u5E93\u4F4D\u4E0D\u5B58\u5728
boxnumber=\u7BB1\u53F7:
auax.stimulate.summit=\u7BB1\u5177\u6709\u4E3B\u8F85\u7BB1\u5173\u7CFB\uFF0C\u5FC5\u987B\u540C\u65F6\u63D0\u4EA4
virtual.box.number=\u6709\u90E8\u5206\u7BB1\u53F7\u7684\u4F53\u79EF\u6216\u91CD\u91CF\u4E3A0\uFF0C\u4E14\u4E0D\u662F\u865A\u62DF\u7BB1\uFF0C\u4E0D\u5141\u8BB8\u5165\u5E93
map.box.pallet=\u8F93\u5165\u7684\u7BB1\u53F7\u5B58\u5728\u6258\u76D8\u4E0D\u5141\u8BB8\u5165\u5E93
ENTRY_WARE_HOUSE=\u5DF2\u7ECF\u5165\u5E93
Pallet.not.canned.end=\u6258\u76D8\u672A\u626B\u63CF\u7ED3\u675F
No.termail.task.is.must.approval.by.factory=\u4E0D\u662F\u7C7B\u7EC8\u7AEF\u4EFB\u52A1\u5FC5\u987B\u68C0\u67E5\u5B58\u5728\u51FA\u5382\u5BA1\u6279\u4FE1\u606F,\u5355\u636E\u53F7:{0},\u4F1A\u7B7E\u4EBA:{1}
Bill.status.not.is.submitted=\u5355\u636E\u72B6\u6001\u4E0D\u4E3A\u5DF2\u63D0\u4EA4\uFF0C\u4E0D\u80FD\u63A5\u6536\uFF01
Taskno.is.not.move.in.erp=\u4EFB\u52A1\u53F7\u672A\u5728ERP\u505A\u5DE5\u5E8F\u79FB\u52A8
Taskno.is.not.exists.in.erp=ERP\u4E2D\u65E0\u8BE5\u4EFB\u52A1\u53F7
Taskno.status.isnot.pack=\u8BE5\u4EFB\u52A1\u5B50\u72B6\u6001\u4E0D\u4E3A\u5305\u88C5
not.canned.end=\u5B58\u5728\u7BB1\u672A\u626B\u63CF\u7ED3\u675F
The.sub.barcode.cannot.be.a.barcode.starting.with.21CA=\u5B50\u6761\u7801\u4E0D\u80FD\u662F21CA\u5F00\u5934\u7684\u6761\u7801
Under.the.whole.machine.task.the.sub.barcode.can.only.have.four.layers.of.material.codes=\u6574\u673A\u7AD9\u70B9\u4E0B\uFF0C\u5B50\u6761\u7801\u53EA\u80FD\u662F\u56DB\u5C42\u7269\u6599\u4EE3\u7801
Under.the.task.of.the.whole.machine.the.primary.barcode.cannot.be.a.two.layer.and.three.layer.material.code=\u6574\u673A\u7AD9\u70B9\u4E0B\uFF0C\u4E3B\u6761\u7801\u4E0D\u80FD\u662F\u4E8C\u5C42\u4E09\u5C42\u7269\u6599\u4EE3\u7801
Box.no.item.no.scaned=\u7BB1\u6CA1\u6709\u7269\u6599\u4FE1\u606F
There.is.no.case.number.on.the.pallet.number=\u6258\u76D8\u4E0A\u6CA1\u6709\u7BB1\u53F7
entry.ware.house=\u6709\u7BB1\u5DF2\u5165\u5E93\u8BF7\u68C0\u67E5
pallent.no.scan.end=\u6258\u76D8\u672A\u626B\u63CF\u7ED3\u675F\uFF0C\u4E0D\u5F97\u5165\u5E93
do.not.enter.empty.box=\u7A7A\u7BB1\u4E0D\u51C6\u5165\u5E93
box.pallet.is.not.exist=\u7BB1\u53F7\u6258\u76D8\u4E0D\u5B58\u5728\uFF0C\u6216\u8005\u6258\u76D8\u7BB1\u53F7\u4E0D\u5728\u5F53\u524D\u4EFB\u52A1\u4E0B
cannot.input.box=\u4E0D\u80FD\u8F93\u5165\u7BB1\u53F7
the.gross.and.netweight.is.not.zero=\u7BB1\u7684\u6BDB\u91CD\u51C0\u91CD\u4E0D\u80FD\u4E3A\u96F6
exists.in.order=\u5DF2\u6709\u5165\u5E93\u5355
Itembarcode.between.0.and.50=\u6761\u7801\u6570\u91CF\u5927\u4E8E0\u5C0F\u4E8E\u7B49\u4E8E50
Pagesize.between.0.and.%d=\u9875\u7801\u5927\u5C0F\u5927\u4E8E0\u5C0F\u4E8E\u7B49\u4E8E%d
query.count.cannot.be.greater.than.500=\u4E00\u6B21\u6700\u591A\u67E5\u8BE2500\u4E2A\u6279\u6B21\uFF01
prodplan.id.null=\u6279\u6B21\u4E0D\u80FD\u4E3A\u7A7A
The.main.barcode.is.21CA.which.must.be.a.complete.machine.site=\u975E\u6574\u673A\u7AD9\u70B9\uFF0C\u4E0D\u80FD\u626B\u63CF21CA\u6761\u7801
redis.lock.fail={0} \u6B63\u5728\u64CD\u4F5C\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5!
sn.miss.board.online={0}\u6761\u7801\u5728board online \u4E0D\u5B58\u5728\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5!
customer.name.isnot.null=\u7528\u6237\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
sn.not.more.than.500=\u670D\u52A1\u5668\u6761\u7801\u4E0D\u80FD\u8D85\u8FC7500
taskno.and.sn.isnot.null=\u4EFB\u52A1\u53F7\u548C\u670D\u52A1\u5668\u6761\u7801\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
submit.time.interval.more.than.180.days=\u63D0\u4EA4\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC7180\u5929
verify.time.interval.more.than.180.days=\u786E\u8BA4\u65F6\u95F4\u8DE8\u5EA6\u4E0D\u80FD\u8D85\u8FC7180\u5929
query.param.error=\u6279\u6B21\u548C\u63D0\u4EA4\u65F6\u95F4\u548C\u786E\u8BA4\u65F6\u95F4\u4E09\u8005\u5FC5\u586B\u5176\u4E00
failed.to.get.redis.lock=\u83B7\u53D6redis\u9501\u8D44\u6E90\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5
call.service.error=\u8C03\u7528\u670D\u52A1\u5F02\u5E38\uFF1A{0}--{1}
failed_to_obtain_customer_basic_information=\u6839\u636E\u7269\u6599\u4EE3\u7801\u83B7\u53D6\u5BA2\u6237\u57FA\u7840\u4FE1\u606F\u5931\u8D25
sys.look.not.config = \u6570\u636E\u5B57\u5178 {0} \u672A\u914D\u7F6E
entity.name.need.user.address = \u8F93\u5165\u4EFB\u52A1\u540D\u79F0\u9700\u8981\u643A\u5E26\u7528\u6237\u5730\u5740
failed_to_call_mds_interface=\u8C03MDS\u63A5\u53E3\u5931\u8D25:{0}
failed_to_get_barcode_center_url=\u83B7\u53D6\u6570\u636E\u5B57\u5178\u914D\u7F6E\u6761\u7801\u4E2D\u5FC3\u63A5\u53E3URL\u5931\u8D25
call.barCode.center.expandQuery.barCode.falied=\u8C03\u6761\u7801\u4E2D\u5FC3\u83B7\u53D6\u6761\u7801\u6269\u5C55\u4FE1\u606F\u5931\u8D25:{0}
get.zs.log.station.error=\u5DE5\u7AD9\u65E5\u5FD7\u63A5\u53E3\u5F02\u5E38
autoflag.is.not.null=\u81EA\u52A8\u4E0A\u4F20\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A
tasks_cannot_exceed_20=\u4EFB\u52A1\u4E0D\u80FD\u8D85\u8FC720\u4E2A
archive.extract.task.executing=\u5F85\u5F52\u6863\u6570\u636E\u540C\u6B65\u4EFB\u52A1\u6B63\u5728\u6267\u884C\u4E2D
archive.task.executing=\u5F52\u6863\u4EFB\u52A1\u6B63\u5728\u6267\u884C\u4E2D
archive.semiprod.null=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5F85\u5F52\u6863\u534A\u6210\u54C1\u5355\u7BA1\u7406\u5355\u636E\u4FE1\u606F\u8FD4\u56DE\u7A7A
archive.semiprod.detail.null=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5F85\u5F52\u6863\u534A\u6210\u54C1\u5355\u7BA1\u7406\u5355\u636E\u660E\u7EC6\u4FE1\u606F\u8FD4\u56DE\u7A7A
archive.payable.null=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5F85\u5F52\u6863\u5E94\u4ED8\u6B3E\u7ED3\u7B97\u5355\u636E\u4FE1\u606F\u8FD4\u56DE\u7A7A
archive.manufacturingorder.server.error=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5236\u9020\u901A\u77E5\u5355\u8FD4\u56DE\u62A5\u9519
unarchive.manufacturingorder.server.error=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5F85\u5F52\u6863\u5236\u9020\u901A\u77E5\u5355\u636E\u8FD4\u56DE\u62A5\u9519
archive.manufacturingorder.download.error=\u6587\u4EF6\u4E0B\u8F7D\u51FA\u9519
archive.testprocess.server.error=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u6D4B\u8BD5\u5DE5\u827A\u5355\u8FD4\u56DE\u62A5\u9519
unarchive.testprocess.server.error=\u8C03\u7528\u670D\u52A1\u83B7\u53D6\u5F85\u5F52\u6863\u6D4B\u8BD5\u5DE5\u827A\u5355\u636E\u8FD4\u56DE\u62A5\u9519
b2b.return.cn=\u4F60\u597D\uFF0C\u5355\u636E{0}\u5B58\u5728b2b\u8FD4\u56DE\u5931\u8D25\u6279\u6B21\uFF0C\u8BF7\u68C0\u67E5\u3002
task.no.not.exist=\u4EFB\u52A1\u53F7{0}\u4E0D\u5B58\u5728
task.no.not.more.than.100=\u4EFB\u52A1\u53F7\u4E0D\u80FD\u8D85\u8FC7100\u4E2A
type.no.is.null=\u672A\u67E5\u8BE2\u5230\u5206\u7C7B\u7F16\u7801
regulation.is.null=\u672A\u67E5\u8BE2\u5230\u4E0D\u5408\u683C\u54C1\u6027\u8D28\u89C4\u5219
master.type.is.null=\u6750\u6599\u5927\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
small.type.is.null=\u6750\u6599\u5C0F\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
sub.type.is.null=\u6750\u6599\u6B64\u5C0F\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
is.item.is.null=\u662F\u5426\u6750\u6599\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
category.is.null=\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
defects.qty.is.null=\u4E0D\u5408\u683C\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
contract.and.task.is.null=\u5408\u540C\u53F7\u548C\u4EFB\u52A1\u53F7\u4E0D\u80FD\u90FD\u4E3A\u7A7A
choose.no.imei.repeat=\u53D1\u8D27\u901A\u77E5\u5355\u53F7IMEI:{0} \u91CD\u590D
choose.no.repeat=\u53D1\u8D27\u901A\u77E5\u5355\u53F7\u91CD\u590D\uFF1A{0}
choose.no.exist=\u53D1\u8D27\u901A\u77E5\u5355\u53F7\u5DF2\u7ECF\u5B58\u5728\uFF1A{0}
imei.box.not.find=\u6CA1\u6709\u627E\u5230\u4E32\u53F7{0}\u7684\u7BB1\u53F7
box.info.not.find={0}\u7BB1\u4FE1\u606F\u672A\u627E\u5230
box.imei.item.diff={0}\u7BB1\u7269\u6599\u4EE3\u7801{1}\u4E0E\u4E32\u53F7{2}\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4
box.imei.sub.diff={0}\u7BB1\u5B50\u5E93\u5B58{1}\u4E0E\u4E32\u53F7{2}\u5B50\u5E93\u5B58\u4E0D\u4E00\u81F4
box.subinventory.not.find={0}\u5C0F\u7BB1\u53F7\u7684\u5B50\u5E93\u5B58\u4E3A\u7A7A\uFF0C\u4E0D\u80FD\u626B\u63CF!
box.warehouse.not.in=\u4E32\u53F7{0}\u5BF9\u5E94\u7684\u7BB1{1}\u4E0D\u662F\u5728\u5E93\u72B6\u6001
imei.scaned=\u4E32\u53F7{0}\u5DF2\u7ECF\u626B\u63CF
bill.no.exists = \u5355\u636E\u53F7\u5DF2\u7ECF\u5B58\u5728 {0}\uFF0C\u8BF7\u786E\u8BA4\uFF01
issue.date.not.null=\u53D1\u6587\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
tray.code.is.null=\u6258\u76D8\u7801\u4E0D\u80FD\u4E3A\u7A7A
outer.box.list.is.null=\u5916\u7BB1\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
en.code.list.is.null=EN\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
mes.info.is.null=MES\u8FD4\u56DE\u7684\u88C5\u7BB1\u4FE1\u606F\u4E3A\u7A7A
forward.and.mtp.test.upload.success.not.exists=\u4E0D\u5B58\u5728\u6B63\u5411\u6570\u636E\u53CAMPT\u6D4B\u8BD5\u65E5\u5FD7\u4E0A\u4F20\u6210\u529F\u7684\u8BB0\u5F55
discrete.param.name.error = \u53C2\u6570\u7C7B\u578B\u4E3A\u6570\u636E\u6BB5\u65F6\uFF0CparamList\u5217\u8868\u957F\u5EA6\u5FC5\u987B\u4E3A2
exist.not.param.name =  \u53C2\u6570\u540D\u79F0\u4E0E\u6570\u636E\u5E93\u4E0D\u5339\u914D
param.list.is.null = \u53C2\u6570\u503C\u4E0D\u80FD\u4E3A\u7A7A
param.type.is.incorrect = \u53C2\u6570\u7C7B\u578B\u4E0D\u6B63\u786E
exist.not.data.type = \u6570\u636E\u7C7B\u578B\u8F93\u5165\u6709\u8BEF
exist.not.param.Repeated = \u6570\u636E\u53C2\u6570\u540D\u79F0\u4E0D\u80FD\u91CD\u590D
item.code.of.mes.is.different=\u6240\u9009\u7269\u6599\u4EE3\u7801\u4E0EMES\u7269\u6599\u4EE3\u7801\u4E0D\u4E00\u81F4
mes.packing.info.is.null=MES\u8FD4\u56DE\u7684\u88C5\u7BB1\u4FE1\u606F\u4E3A\u7A7A
failed_to_specified_ps_task=\u67E5\u8BE2\u8F66\u95F4\u8BA1\u5212_\u4EFB\u52A1\u4FE1\u606F\u83B7\u53D6\u63A5\u53E3\u5931\u8D25
item_control_entry_fail=\u7279\u6b8a\u7269\u6599{0}\u7ba1\u63a7\uff0c{1}\u4efb\u52a1\u7981\u6b62\u53d1\u6599\uff0c\u8bf7\u8054\u7cfb\u4e1a\u52a1\u4ee3\u8868\u5f20\u6653\u6c9f\u901a\uff1b
required_fields_cannot_be_empty=\u5fc5\u586b\u5b57\u6bb5{0}\u4e0d\u80fd\u4e3a\u7a7a