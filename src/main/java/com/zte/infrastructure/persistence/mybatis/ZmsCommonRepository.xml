<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.datawb.ZmsCommonRepository">

    <resultMap id="BaseResultMap" type="com.zte.interfaces.dto.ZmsCommonEntityDTO">
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName"/>
        <result column="ENTITY_ID" jdbcType="VARCHAR" property="entityId"/>
        <result column="CONTRACT_HEADER_ID" jdbcType="VARCHAR" property="contractHeaderId"/>
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber"/>
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.zte.interfaces.dto.ECMaterialAssemblyDTO">
        <result column="ENTITY_NAME" jdbcType="VARCHAR" property="entityName"/>
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber"/>
        <result column="ORGANIZATION_ID" jdbcType="VARCHAR" property="orgId"/>
    </resultMap>

    <select id="getZmsEntityList" parameterType="com.zte.interfaces.dto.ZmsCommonRequestDTO" resultMap="BaseResultMap">
        SELECT CCE.ENTITY_NAME,
               CCE.ENTITY_ID,
               CCH.CONTRACT_HEADER_ID,
               CCH.CONTRACT_NUMBER
          FROM WMES.CPM_CONTRACT_ENTITIES   CCE,
               WMES.CDM_CONTRACT_LINES      CCL,
               WMES.CDM_CONTRACT_HEADERS    CCH,
               WMES.WERP_CON_ENTITY_TRACE   WCET
         WHERE CCE.CONTRACT_LINE_ID = CCL.CONTRACT_LINE_ID
           AND CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
           AND CCE.ENABLED_FLAG = 'Y'
           AND CCE.ENTITY_ID = WCET.ENTITY_ID
        <if test="entityNameList != null and entityNameList.size() > 0">
            AND CCE.ENTITY_NAME IN
            <foreach collection="entityNameList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="entityNameList == null or entityNameList.size() == 0">
            AND CCE.ORGANIZATION_ID IN
            <foreach collection="organizationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            AND CCE.USER_ADDRESS IN (
            SELECT T.DESCRIPTION
            FROM MESSYS.SYS_LOOKUP_VALUES T
            WHERE T.LOOKUP_TYPE = '3020041'
            AND T.LOOKUP_MEANING = #{userAddress,jdbcType=VARCHAR}
            )
            AND CCE.ENTITY_NAME LIKE '%${entityNameLike}%'
            AND WCET.STATUS_NAME IN
            <foreach collection="statusNameList" index="index" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getZmsEntityListByEntityId" resultMap="BaseResultMap1">
        SELECT
            CCE.ENTITY_NAME,
            CCH.CONTRACT_NUMBER,
            CCE.ORGANIZATION_ID,
        FROM
            APP_MES.CPM_CONTRACT_ENTITIES CCE,
            APP_MES.CDM_CONTRACT_LINES CCL,
            APP_MES.CDM_CONTRACT_HEADERS CCH
        WHERE
            CCE.CONTRACT_LINE_ID = CCL.CONTRACT_LINE_ID
            AND CCL.CONTRACT_HEADER_ID = CCH.CONTRACT_HEADER_ID
            AND CCE.ENABLED_FLAG = 'Y'
            AND CCE.ENTITY_ID = #{entityId}
            AND CCH.RECORD_SOURCE_TYPE = 'CON'
    </select>

    <insert id="insertInternetMain" parameterType="com.zte.interfaces.dto.ZmsInternetMainDTO">
        MERGE INTO WMES.ZMS_INTERNET_MAIN T
        USING (select #{customerName,jdbcType = VARCHAR} customerName,
                      #{contractHeaderId,jdbcType = VARCHAR} contractHeaderId,
                      #{contractNumber,jdbcType = VARCHAR} contractNumber,
                      #{entityId,jdbcType = VARCHAR} entityId,
                      #{entityName,jdbcType = VARCHAR} entityName,
                      #{serverSn,jdbcType = VARCHAR} serverSn,
                      #{dataType,jdbcType = VARCHAR} dataType,
                      #{failReason,jdbcType = VARCHAR} failReason,
                      #{qCode,jdbcType = VARCHAR} qCode,
                      #{dataUpStatus,jdbcType = VARCHAR} dataUpStatus,
                      #{createdBy,jdbcType = VARCHAR} createdBy,
                      #{lastUpdatedBy,jdbcType = VARCHAR} lastUpdatedBy
                 from dual) A
        ON (T.SERVER_SN = A.serverSn and T.DATA_TYPE = A.dataType)
        WHEN MATCHED THEN
          UPDATE
             SET T.customer_name      = A.customerName,
                 T.contract_header_id = A.contractHeaderId,
                 T.contract_number    = A.contractNumber,
                 T.entity_id          = A.entityId,
                 T.entity_name        = A.entityName,
                 T.fail_reason        = A.failReason,
                 T.qcode              = A.qCode,
                 T.last_updated_by    = A.lastUpdatedBy,
                 T.data_up_status     = A.dataUpStatus,
                 T.last_update_date   = sysdate
        WHEN NOT MATCHED THEN
          INSERT
            (record_id,
             customer_name,
             contract_header_id,
             contract_number,
             entity_id,
             entity_name,
             server_sn,
             data_type,
             fail_reason,
             qcode,
             key_message,
             data_up_status,
             created_by,
             last_updated_by)
          VALUES
            (WMES.ZMS_INTERNET_MAIN_S.NEXTVAL,
             A.customerName,
             A.contractHeaderId,
             A.contractNumber,
             A.entityId,
             A.entityName,
             A.serverSn,
             A.dataType,
             A.failReason,
             A.qCode,
             'Y',
             A.dataUpStatus,
             A.createdBy,
             A.lastUpdatedBy)
    </insert>

    <update id="updateInternetMain" parameterType="com.zte.interfaces.dto.ZmsInternetMainDTO">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update wmes.zms_internet_main
            <set>
                <if test="item != null and item.dataUpStatus != null and item.dataUpStatus != ''">
                    data_up_status = #{item.dataUpStatus,jdbcType=VARCHAR},
                </if>
                <if test="item != null and item.failReason != null and item.failReason != ''">
                    fail_reason = #{item.failReason,jdbcType=VARCHAR},
                </if>
                last_update_date = sysdate
            </set>
            <where>
                enabled_flag = 'Y'
                and server_sn = #{item.serverSn,jdbcType=VARCHAR}
                and data_type = #{item.dataType,jdbcType=VARCHAR}
                <if test="item != null and item.dataUpStatus != null and item.dataUpStatus != ''
                and (item.dataUpStatus == '1' or item.dataUpStatus == '2')">
                    and data_up_status not in ('-1','-2')
                </if>
            </where>
        </foreach>
    </update>

    <select id="getInternetMain" parameterType="java.lang.String" resultType="java.lang.String">
        select distinct t.server_sn
        from WMES.ZMS_INTERNET_MAIN t
        where t.data_up_status != '0'
        and t.data_type = #{dataType,jdbcType=VARCHAR}
        and t.server_sn in
        <foreach collection="snList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insertMesInfoUploadLog" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close="end;">
            insert into wmes.zms_mes_info_upload_log
            (id, origin, customer_name, project_name, project_phase, cooperation_mode, message_type, craft_section, contract_no,
            task_no, item_no, sn, json_data, factory_id, status, remark, create_by, last_updated_by)
            values (#{item.id,jdbcType=VARCHAR}, #{item.origin,jdbcType=VARCHAR}, #{item.customerName,jdbcType=VARCHAR},
            #{item.projectName,jdbcType=VARCHAR}, #{item.projectPhase,jdbcType=VARCHAR}, #{item.cooperationMode,jdbcType=VARCHAR},
            #{item.messageType,jdbcType=VARCHAR}, #{item.craftSection,jdbcType=VARCHAR}, #{item.contractNo,jdbcType=VARCHAR},
            #{item.taskNo,jdbcType=VARCHAR}, #{item.itemNo,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR},
            empty_clob(), #{item.factoryId,jdbcType=INTEGER}, #{item.status,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, 'system', 'system');
            <if test="item.jsonData != null">
                update wmes.zms_mes_info_upload_log set json_data = #{item.jsonData,jdbcType=CLOB} where id = #{item.id,jdbcType=VARCHAR};
            </if>
        </foreach>
    </insert>

    <select id="getSysLookupValues" resultType="com.zte.domain.model.SysLookupValues">
        select b.*
        from messys.sys_lookup_values b,
        messys.sys_lookup_types a
        where a.lookup_type = b.lookup_type
        and a.enabled_flag = 'Y'
        and b.enabled_flag = 'Y'
        and b.lookup_code = #{lookupCode,jdbcType=VARCHAR}
    </select>

    <select id="getSysLookupValuesList" resultType="com.zte.domain.model.SysLookupValues">
        select b.*
        from messys.sys_lookup_values b,
        messys.sys_lookup_types a
        where a.lookup_type = b.lookup_type
        and a.enabled_flag = 'Y'
        and b.enabled_flag = 'Y'
        and b.lookup_type = #{lookupType,jdbcType=VARCHAR}
    </select>

    <select id="getAssemblyMaterialList" parameterType="java.util.List" resultType="com.zte.interfaces.dto.WsmAssembleLinesEntityDTO">
        select t.SCAN_TYPE,t.ITEM_BARCODE,t.ITEM_QTY,
        t.ITEM_CODE,t.ITEM_NAME
        from sfc.wsm_assemble_headers t1,sfc.wsm_assemble_lines t
        where t.assemble_headers_id = t1.assemble_headers_id
        and t.enabled_flag = 'Y' and t1.enabled_flag = 'Y'
        AND t1.ITEM_BARCODE IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>

        union all

        select t.BARCODE_TYPE SCAN_TYPE,t.ITEMBARCODE ITEM_BARCODE,t.ITEMQTY ITEM_QTY,
        t.ITEMCODE ITEM_CODE,t.ITEMNAME ITEM_NAME
        from sfc.wsm_assemable_imes_inf t
        where t.enabled_flag = 'Y'
        and t.mainbarcode in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getAssemblyMaterialsByEntityId"   resultType="com.zte.interfaces.dto.CpmConfigItemAssembleDTO">
        select ENTITY_ID,ITEM_BARCODE,BARCODE_TYPE,BARCODE_QTY,ITEM_CODE,ITEM_ID,ITEM_NAME
        from SFC.cpm_config_item_assemble t
        where t.entity_id = #{entityId}
        and t.enabled_flag = 'Y'
    </select>

    <select id="getCbomInfoNoVersion" parameterType="com.zte.interfaces.dto.ZmsCommonRequestDTO" resultType="com.zte.interfaces.dto.ZmsCbomInfoDTO">
        SELECT DISTINCT
            CASE WHEN INSTR(B.DESCRIPTION, #{intercept}) > 0
            THEN SUBSTR(B.DESCRIPTION, 1, INSTR(B.DESCRIPTION, #{intercept}) - 1)
            ELSE B.DESCRIPTION
            END cbomNameCn,
            CCE.ENTITY_NAME entityName
        FROM APP_MES.MTL_SYSTEM_ITEMS  B,
        APP_MES.CPM_CONFIG_DETAILS     CCD,
        APP_MES.CPM_CONTRACT_MFG_SITES MFG,
        app_mes.cpm_contract_entities  cce
        WHERE CCD.ORGANIZATION_ID = B.ORGANIZATION_ID
        AND CCD.INVENTORY_ITEM_ID = B.INVENTORY_ITEM_ID
        AND CCD.ENABLED_FLAG = 'Y'
        AND MFG.ENABLED_FLAG = 'Y'
        AND CCD.MFG_SITE_ID = MFG.MFG_SITE_ID
        and cce.entity_id = mfg.entity_id
        and cce.entity_name in
        <foreach collection="entityNameList" item="item" separator=',' open='(' close=')' index="index">
            #{item, jdbcType=VARCHAR}
        </foreach>
        and B.DESCRIPTION like '%${descLike}%'
    </select>
</mapper>