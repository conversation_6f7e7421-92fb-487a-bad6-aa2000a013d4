package com.zte.domain.model.datawb;

import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ZmsCommonRepository {

    /**
     * 查询互联网任务
     */
    List<ZmsCommonEntityDTO> getZmsEntityList(ZmsCommonRequestDTO dto);

    /**
     * 根据任务id查询合同号
     */
    List<ECMaterialAssemblyDTO> getZmsEntityListByEntityId(@Param("entityId") Integer entityId);

    /**
     * 互联网传输主表插入
     */
    int insertInternetMain(ZmsInternetMainDTO dto);

    /**
     * 更新互联网传输主表
     */
    int updateInternetMain(List<ZmsInternetMainDTO> list);

    /**
     * 查询互联网传输主表数据
     */
    List<String> getInternetMain(@Param("dataType") String dataType, @Param("snList") List<String> snList);

    /**
     * 写入上传日志
     */
    int insertMesInfoUploadLog(List<ZmsMesInfoUploadLogDTO> list);

    /**
     * 查询单个数据字典
     */
    SysLookupValues getSysLookupValues(@Param("lookupCode") String lookupCode);

    /**
     * 查询批量数据字典
     */
    List<SysLookupValues> getSysLookupValuesList(@Param("lookupType") String lookupType);

    /**
     * 查询装配关系
     */
    List<WsmAssembleLinesEntityDTO> getAssemblyMaterialList(List<String> list);

    /**
     * 根据任务id获取装配物料
     */
    List<CpmConfigItemAssembleDTO> getAssemblyMaterialsByEntityId(@Param("entityId") Integer entityId);

    /**
     * 根据任务号获取无版本号的套餐名称
     */
    List<ZmsCbomInfoDTO> getCbomInfoNoVersion(ZmsCommonRequestDTO dto);


}
