package com.zte.application.datawb.impl;

import com.zte.application.datawb.ECCfgMaterialAssemblyRelService;
import com.zte.application.datawb.WsmAssembleLinesService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.WsmAssembleLinesRepository;
import com.zte.domain.model.datawb.ZmsCommonRepository;
import com.zte.interfaces.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.springbootframe.common.Constants.STR_ONE;

/**
 * 配置物料装配关系查询实现类
 *
 * <AUTHOR>
 * @date 2025-08-04 10:20:00
 */
@Service
public class ECCfgMaterialAssemblyRelServiceImpl implements ECCfgMaterialAssemblyRelService {

    @Autowired
    private WsmAssembleLinesRepository wsmAssembleLinesRepository;

    @Autowired
    private ZmsCommonRepository zmsCommonRepository;

    @Autowired
    private WsmAssembleLinesService wsmAssembleLinesService;

    /**
     * 根据服务器SN列表查询物料装配关系
     *
     * @param serverSnList 服务器SN列表
     * @return 物料装配关系列表
     */
    @Override
    public List<ECMaterialAssemblyDTO> getAssemblyRelationList(List<String> serverSnList) {
        List<ECMaterialAssemblyDTO> list = new ArrayList<>();
        for (String serverSn : serverSnList) {
            List<CpmConfigItemAssembleDTO> cpmConfigItemAssembleDTOList = wsmAssembleLinesRepository.getAssemblyMaterialsByServerSn(serverSn);
            if (CollectionUtils.isEmpty(cpmConfigItemAssembleDTOList)) {
                continue;
            }
            // 根据EntityId获取任务对应的装配物料
            List<CpmConfigItemAssembleDTO> configDetailDTOList = wsmAssembleLinesService.getAssemblyMaterialsByEntityId(
                    Integer.valueOf(cpmConfigItemAssembleDTOList.get(0).getEntityId()));
            if (CollectionUtils.isEmpty(configDetailDTOList)) {
                continue;
            }

            // == 获取服务器sn对应的装配物料 ==
            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList = wsmAssembleLinesService.getAssemblyMaterialsWithEntityName(serverSn);
            if (CollectionUtils.isEmpty(wsmAssembleLinesList)) {
                return null;
            }
            // 部件信息查询需要查询四层，物料代码为1开头的需要再向下查询
            getAllWsmAssembleLines(wsmAssembleLinesList);

            // 物料代码去重
            List<String> itemCodeList = configDetailDTOList.stream().map(CpmConfigItemAssembleDTO::getItemCode)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            // 机框模组物料代码字典查询
            List<SysLookupValues> lookupValues = wsmAssembleLinesRepository.getSysLookupValues(Constant.LOOKUP_TYPE_MODULE_ITEM_CODE);
            List<String> shelfModItemCodeList = lookupValues.stream().map(SysLookupValues::getDescription)
                    .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            List<String> shelfModItemBarcodeList = configDetailDTOList.stream().filter(i ->
                    shelfModItemCodeList.contains(i.getItemCode())).map(CpmConfigItemAssembleDTO::getItemBarcode).collect(Collectors.toList());
            // 机框模组查询四层装配关系
            getFourWsmAssembleLines(shelfModItemBarcodeList, wsmAssembleLinesList);

            // 获取合同信息(合同号、任务号、组织ID)
            List<ECMaterialAssemblyDTO> ecMaterialAssemblyDTOList = zmsCommonRepository.getZmsEntityListByEntityId(
                    Integer.valueOf(cpmConfigItemAssembleDTOList.get(0).getEntityId()));
            // 组装结果
            list.add(convertToMaterialAssemblyDTO(serverSn, ecMaterialAssemblyDTOList, wsmAssembleLinesList));
        }
        return list;
    }

    /**
     * 转换为物料装配关系DTO
     *
     * @param serverSn 服务器SN
     * @param ecMaterialAssemblyDTOList 合同信息列表
     * @param wsmAssembleLinesList 装配关系列表
     * @return 物料装配关系DTO
     */
    private ECMaterialAssemblyDTO convertToMaterialAssemblyDTO(String serverSn,
                                                               List<ECMaterialAssemblyDTO> ecMaterialAssemblyDTOList,
                                                               List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {
        ECMaterialAssemblyDTO materialAssemblyDTO = new ECMaterialAssemblyDTO();
        // 服务器SN
        materialAssemblyDTO.setServerSn(serverSn);
        if (!CollectionUtils.isEmpty(ecMaterialAssemblyDTOList)) {
            // 合同号
            materialAssemblyDTO.setContractNumber(ecMaterialAssemblyDTOList.get(0).getContractNumber());
            // 任务号
            materialAssemblyDTO.setEntityName(ecMaterialAssemblyDTOList.get(0).getEntityName());
            // 组织ID
            materialAssemblyDTO.setOrgId(ecMaterialAssemblyDTOList.get(0).getOrgId());
        }
        // 组装明细列表
        if(!CollectionUtils.isEmpty(wsmAssembleLinesList)) {
            List<ECMaterialAssemblyItemDTO> assembleList = new ArrayList<>();
            for(ECWsmAssembleLinesEntityWithNameDTO wsmAssembleLinesEntityWithNameDTO : wsmAssembleLinesList) {
                ECMaterialAssemblyItemDTO materialAssemblyItemDTO = new ECMaterialAssemblyItemDTO();
                // 子条码
                materialAssemblyItemDTO.setItemBarcode(wsmAssembleLinesEntityWithNameDTO.getItemBarcode());
                // 条码类型
                materialAssemblyItemDTO.setItemType(wsmAssembleLinesEntityWithNameDTO.getScanType());
                // 物料代码
                materialAssemblyItemDTO.setItemCode(wsmAssembleLinesEntityWithNameDTO.getItemCode());
                // 物料名称
                materialAssemblyItemDTO.setItemName(wsmAssembleLinesEntityWithNameDTO.getItemName());
                // 数量
                materialAssemblyItemDTO.setQty(wsmAssembleLinesEntityWithNameDTO.getItemQty());
                // 父条码
                materialAssemblyItemDTO.setParentItemBarcode(wsmAssembleLinesEntityWithNameDTO.getParentItemBarcode());
                // 自建任务号
                materialAssemblyItemDTO.setWpEntityName(wsmAssembleLinesEntityWithNameDTO.getEntityName());
                // 绑定人
                // TODO 是否转换成名称待定
                materialAssemblyItemDTO.setAssembleby(String.valueOf(wsmAssembleLinesEntityWithNameDTO.getCreatedBy()));
                // 绑定时间
                materialAssemblyItemDTO.setAssembleDate(wsmAssembleLinesEntityWithNameDTO.getCreationDate());
                assembleList.add(materialAssemblyItemDTO);
            }
            materialAssemblyDTO.setAssembleList(assembleList);
        }
        return materialAssemblyDTO;
    }

    /**
     * 部件信息查询需要查询四层，物料代码为1开头的需要再向下查询
     *
     * @param wsmAssembleLinesList 装配关系列表，既是输入也是输出
     */
    public void getAllWsmAssembleLines(List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {

        // 第二层查询
        List<String> itemBarcodeList2 = wsmAssembleLinesList.stream()
                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(itemBarcodeList2)) {
            // 查询第2层
            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList2 =
                    wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList2);

            if (!CollectionUtils.isEmpty(wsmAssembleLinesList2)) {
                wsmAssembleLinesList.addAll(wsmAssembleLinesList2);

                // 第三层查询
                List<String> itemBarcodeList3 = wsmAssembleLinesList2.stream()
                        .filter(i -> i.getItemCode().startsWith(STR_ONE))
                        .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(itemBarcodeList3)) {
                    // 查询第3层
                    List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList3 =
                            wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList3);

                    if (!CollectionUtils.isEmpty(wsmAssembleLinesList3)) {
                        wsmAssembleLinesList.addAll(wsmAssembleLinesList3);

                        // 第四层查询
                        List<String> itemBarcodeList4 = wsmAssembleLinesList3.stream()
                                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                                .collect(Collectors.toList());

                        if (!CollectionUtils.isEmpty(itemBarcodeList4)) {
                            // 查询第4层
                            List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList4 =
                                    wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList4);

                            if (!CollectionUtils.isEmpty(wsmAssembleLinesList4)) {
                                wsmAssembleLinesList.addAll(wsmAssembleLinesList4);
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 机框模组查询四层装配关系
     *
     * @param itemBarcodeList 物料条码列表
     * @param wsmAssembleLinesList 装配关系列表，既是输入也是输出
     */
    public void getFourWsmAssembleLines(List<String> itemBarcodeList, List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList) {

        // 第一层
        if (CollectionUtils.isEmpty(itemBarcodeList)) {
            return;
        }
        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList1 =
                wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList1)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList1);
        }

        // 第二层
        List<String> itemBarcodeList2 = wsmAssembleLinesList1.stream()
                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemBarcodeList2)) {
            // 如果没有以"1"开头的物料代码，尝试递归查询其他物料
            List<String> newitemBarcodeList = wsmAssembleLinesList1.stream()
                    .map(m -> m.getItemBarcode())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newitemBarcodeList)) {
                return;
            }
            newitemBarcodeList.removeAll(itemBarcodeList);
            getFourWsmAssembleLines(newitemBarcodeList, wsmAssembleLinesList);
            return;
        }

        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList2 =
                wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList2);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList2)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList2);
        }

        // 第三层
        List<String> itemBarcodeList3 = wsmAssembleLinesList2.stream()
                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemBarcodeList3)) {
            return;
        }

        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList3 =
                wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList3);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList3)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList3);
        }

        // 第四层
        List<String> itemBarcodeList4 = wsmAssembleLinesList3.stream()
                .filter(i -> i.getItemCode().startsWith(STR_ONE))
                .map(WsmAssembleLinesEntityDTO::getItemBarcode)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemBarcodeList4)) {
            return;
        }

        List<ECWsmAssembleLinesEntityWithNameDTO> wsmAssembleLinesList4 =
                wsmAssembleLinesService.getAssemblyMaterialListWithEntityName(itemBarcodeList4);
        if (!CollectionUtils.isEmpty(wsmAssembleLinesList4)) {
            wsmAssembleLinesList.addAll(wsmAssembleLinesList4);
        }
    }
}
