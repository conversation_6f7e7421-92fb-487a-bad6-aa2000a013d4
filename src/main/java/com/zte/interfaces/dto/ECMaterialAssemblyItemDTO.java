package com.zte.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 配置物料装配信息明细
 *
 * <AUTHOR>
 * @date 2025-08-04 10:20:00
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel("配置物料装配信息明细DTO")
public class ECMaterialAssemblyItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子条码
     */
    @ApiModelProperty(value = "子条码")
    private String itemBarcode;

    /**
     * 条码类型
     */
    @ApiModelProperty(value = "条码类型")
    private String itemType;

    /**
     * 物料代码
     */
    @ApiModelProperty(value = "物料代码")
    private String itemCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String itemName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer qty;

    /**
     * 父条码
     */
    @ApiModelProperty(value = "父条码")
    private String parentItemBarcode;

    /**
     * 自建任务号
     */
    @ApiModelProperty(value = "目标任务号")
    private String wpEntityName;

    /**
     * 绑定人
     */
    @ApiModelProperty(value = "绑定人")
    private String assembleby;

    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date assembleDate;

    // Getter and Setter methods
    public String getItemBarcode() {
        return itemBarcode;
    }

    public void setItemBarcode(String itemBarcode) {
        this.itemBarcode = itemBarcode;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getParentItemBarcode() {
        return parentItemBarcode;
    }

    public void setParentItemBarcode(String parentItemBarcode) {
        this.parentItemBarcode = parentItemBarcode;
    }

    public String getWpEntityName() {
        return wpEntityName;
    }

    public void setWpEntityName(String wpEntityName) {
        this.wpEntityName = wpEntityName;
    }

    public String getAssembleby() {
        return assembleby;
    }

    public void setAssembleby(String assembleby) {
        this.assembleby = assembleby;
    }

    public Date getAssembleDate() {
        return assembleDate;
    }

    public void setAssembleDate(Date assembleDate) {
        this.assembleDate = assembleDate;
    }
}
